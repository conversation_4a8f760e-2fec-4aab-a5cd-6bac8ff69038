# AI模型文件存放目录

## 目录结构

```
models/
├── btc_predictor/          # BTC预测模型
│   ├── btc_predictor.onnx  # BTC预测模型文件
│   ├── btc_predictor.pkl   # 备用Pickle格式
│   └── model_info.json    # 模型信息和版本
├── trading_ai/             # 交易决策模型
│   ├── trading_ai.onnx     # 交易决策模型文件
│   ├── trading_ai.pkl      # 备用Pickle格式
│   └── model_info.json    # 模型信息和版本
└── backup/                 # 模型备份目录
    ├── btc_predictor_v1.0.0/
    └── trading_ai_v1.0.0/
```

## 模型文件要求

1. **支持格式**：ONNX（推荐）或Pickle
2. **命名规范**：模型名称.扩展名
3. **版本管理**：通过model_info.json记录版本信息
4. **备份策略**：每次更新前自动备份到backup目录

## 模型信息格式

model_info.json示例：
```json
{
    "model_name": "btc_predictor",
    "version": "1.0.0",
    "format": "onnx",
    "created_at": "2023-08-04T12:00:00+08:00",
    "input_shape": [1, 24, 5],
    "output_shape": [1, 3],
    "description": "BTC未来1小时走势预测模型"
}
```

## 使用说明

请将你的预训练模型文件放置在对应的子目录中：
- BTC预测模型 → `models/btc_predictor/`
- 交易决策模型 → `models/trading_ai/`