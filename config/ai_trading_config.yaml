# AI交易系统配置文件

system:
  timezone: "Asia/Shanghai"
  log_level: "INFO"
  trader_id: "AI-TRADER-001"

# AI模型配置
ai_models:
  btc_predictor:
    model_path: "models/btc_predictor/model.onnx"
    backup_path: "models/backup/btc_predictor"
    version: "1.0.0"
    update_interval: 3600  # 1小时
    input_features: ["open", "high", "low", "close", "volume"]
    prediction_horizon: 3600  # 1小时预测
  
  trading_ai:
    model_path: "models/trading_ai/trading_ai.onnx"
    backup_path: "models/backup/trading_ai"
    version: "1.0.0"
    inference_timeout: 100  # 毫秒
    input_features: ["ohlcv", "btc_signal", "technical_indicators"]

# Redis缓存配置
redis:
  host: "***************"
  port: 6379
  db: 2
  password: Test@2023
  connection_timeout: 5
  max_connections: 10

# SQLite本地缓存配置（作为Redis备用缓存）
sqlite:
  db_path: "data/cache.db"
  timeout: 30.0              # 数据库操作超时时间（秒）
  check_same_thread: false   # 允许多线程访问
  backup_interval: 3600      # 自动备份间隔（秒）
  backup_path: "data/backups/cache_backup.db"
  auto_cleanup: true         # 自动清理过期数据
  cleanup_interval: 1800     # 清理间隔（秒）

# 风险管理配置
risk_management:
  max_trade_risk: 0.02      # 单笔最大风险2%
  exposure_limit: 0.15      # 单币种敞口限制15%
  max_leverage: 10          # 最大杠杆10倍
  volatility_threshold: 0.1 # 波动率调整阈值10%
  leverage_adjustment_threshold: 0.5  # 杠杆调整阈值0.5
  
  # 熔断机制
  circuit_breaker:
    daily_loss_limit: 0.05    # 单日最大亏损5%
    volatility_limits:
      level_1: 0.03  # 3%波动率：降杠杆至5x
      level_2: 0.05  # 5%波动率：禁用AI切换规则引擎
      level_3: 0.07  # 7%波动率：仅允许平仓

# 交易策略配置
strategies:
  trend_following:
    enabled: true
    confidence_threshold: 0.6
    btc_weight: 0.3           # BTC信号权重
    pyramid_sizing:
      initial_position: 0.005  # 首仓0.5%
      max_position: 0.02       # 最大仓位2%
      increment: 0.005         # 每次加仓0.5%
  
  grid_trading:
    enabled: true
    base_spacing: 0.005       # 基础网格间距0.5%
    max_levels: 10            # 最大网格层级
    btc_sideways_threshold: 0.7  # BTC震荡判断阈值
    optimization:
      spacing_reduction: 0.8   # 间距缩小比例
      level_increase: 1.2      # 层级增加比例

# 性能监控配置
monitoring:
  latency_thresholds:
    mainstream_coins: 100     # 主流币种延迟阈值(ms)
    other_coins: 100          # 其他币种延迟阈值(ms)
    ai_inference: 50          # AI推理延迟阈值(ms)
  
  performance_metrics:
    - "ai_inference_latency"
    - "order_execution_latency"
    - "trade_success_rate"
    - "daily_pnl"
    - "max_drawdown"

# Nautilus Trader配置
nautilus:
  trader_id: "AI-TRADER-PROD"
  log_level: "INFO"
  
  # 支持的交易对配置
  supported_instruments:
    # 现货交易对
    spot:
      - "BTC/USDT"
      - "ETH/USDT"
      - "BNB/USDT"
      - "ADA/USDT"
      - "XRP/USDT"
      - "SOL/USDT"
      - "DOT/USDT"
      - "DOGE/USDT"
      - "AVAX/USDT"
      - "MATIC/USDT"
      - "LINK/USDT"
      - "UNI/USDT"
      - "LTC/USDT"
      - "BCH/USDT"
      - "ETC/USDT"
    
    # 合约交易对（永续合约）
    futures:
      - "BTCUSDT-PERP"
      - "ETHUSDT-PERP"
      - "BNBUSDT-PERP"
      - "ADAUSDT-PERP"
      - "XRPUSDT-PERP"
      - "SOLUSDT-PERP"
      - "DOTUSDT-PERP"
      - "DOGEUSDT-PERP"
      - "AVAXUSDT-PERP"
      - "MATICUSDT-PERP"
      - "LINKUSDT-PERP"
      - "UNIUSDT-PERP"
      - "LTCUSDT-PERP"
      - "BCHUSDT-PERP"
      - "ETCUSDT-PERP"
  
  # 数据获取配置
  data_config:
    default_timeframe: "1-MINUTE"
    supported_timeframes:
      - "1-MINUTE"
      - "5-MINUTE"
      - "15-MINUTE"
      - "1-HOUR"
      - "4-HOUR"
      - "1-DAY"
    max_bars_per_request: 1000
    cache_duration: 300  # 缓存5分钟
    retry_attempts: 3
    retry_delay: 1.0
  
  # 数据客户端配置
  data_clients:
    binance:
      # 从环境变量获取API密钥，或在生产环境中配置
      api_key: "32bRgVtc2BOpUuLvZfspoBkyPyNZVqw7w6QAKa0pJl17AJl4R75H25IYwTcAvVth"
      api_secret: "YuZVXMNlf1LuxvxFFdZYptOCE43sYJEdIDBQj4LCU3H2j7hOpbPaf0Y6UNXSSJVR"
      testnet: false  # 使用主网获取真实数据
      base_url_http: null  # 使用默认URL
      base_url_ws: null    # 使用默认URL
      account_type: "spot"  # spot 或 futures
      
    binance_futures:
      api_key: "32bRgVtc2BOpUuLvZfspoBkyPyNZVqw7w6QAKa0pJl17AJl4R75H25IYwTcAvVth"
      api_secret: "YuZVXMNlf1LuxvxFFdZYptOCE43sYJEdIDBQj4LCU3H2j7hOpbPaf0Y6UNXSSJVR"
      testnet: false
      base_url_http: null
      base_url_ws: null
      account_type: "futures"
  
  # 执行客户端配置
  exec_clients:
    binance:
      api_key: "${BINANCE_API_KEY:-32bRgVtc2BOpUuLvZfspoBkyPyNZVqw7w6QAKa0pJl17AJl4R75H25IYwTcAvVth}"
      api_secret: "${BINANCE_API_SECRET:-YuZVXMNlf1LuxvxFFdZYptOCE43sYJEdIDBQj4LCU3H2j7hOpbPaf0Y6UNXSSJVR}"
      testnet: true  # 执行使用测试网，避免真实交易
      base_url_http: null  # 使用默认URL