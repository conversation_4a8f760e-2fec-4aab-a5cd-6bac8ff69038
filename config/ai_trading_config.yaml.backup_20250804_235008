# AI交易系统配置文件

system:
  timezone: "Asia/Shanghai"
  log_level: "INFO"
  trader_id: "AI-TRADER-001"

# AI模型配置
ai_models:
  btc_predictor:
    model_path: "models/btc_predictor/model.onnx"
    backup_path: "models/backup/btc_predictor"
    version: "1.0.0"
    update_interval: 3600  # 1小时
    input_features: ["open", "high", "low", "close", "volume"]
    prediction_horizon: 3600  # 1小时预测
  
  trading_ai:
    model_path: "models/trading_ai/trading_ai.onnx"
    backup_path: "models/backup/trading_ai"
    version: "1.0.0"
    inference_timeout: 100  # 毫秒
    input_features: ["ohlcv", "btc_signal", "technical_indicators"]

# Redis缓存配置
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null
  connection_timeout: 5
  max_connections: 10

# SQLite本地缓存配置
sqlite:
  db_path: "data/ai_trading.db"
  backup_interval: 3600  # 1小时备份一次

# 风险管理配置
risk_management:
  max_trade_risk: 0.02      # 单笔最大风险2%
  exposure_limit: 0.15      # 单币种敞口限制15%
  max_leverage: 10          # 最大杠杆10倍
  volatility_threshold: 0.1 # 波动率调整阈值10%
  leverage_adjustment_threshold: 0.5  # 杠杆调整阈值0.5
  
  # 熔断机制
  circuit_breaker:
    daily_loss_limit: 0.05    # 单日最大亏损5%
    volatility_limits:
      level_1: 0.03  # 3%波动率：降杠杆至5x
      level_2: 0.05  # 5%波动率：禁用AI切换规则引擎
      level_3: 0.07  # 7%波动率：仅允许平仓

# 交易策略配置
strategies:
  trend_following:
    enabled: true
    confidence_threshold: 0.6
    btc_weight: 0.3           # BTC信号权重
    pyramid_sizing:
      initial_position: 0.005  # 首仓0.5%
      max_position: 0.02       # 最大仓位2%
      increment: 0.005         # 每次加仓0.5%
  
  grid_trading:
    enabled: true
    base_spacing: 0.005       # 基础网格间距0.5%
    max_levels: 10            # 最大网格层级
    btc_sideways_threshold: 0.7  # BTC震荡判断阈值
    optimization:
      spacing_reduction: 0.8   # 间距缩小比例
      level_increase: 1.2      # 层级增加比例

# 性能监控配置
monitoring:
  latency_thresholds:
    mainstream_coins: 100     # 主流币种延迟阈值(ms)
    other_coins: 300          # 其他币种延迟阈值(ms)
    ai_inference: 50          # AI推理延迟阈值(ms)
  
  performance_metrics:
    - "ai_inference_latency"
    - "order_execution_latency"
    - "trade_success_rate"
    - "daily_pnl"
    - "max_drawdown"

# Nautilus Trader配置
nautilus:
  # 数据客户端配置
  data_clients:
    binance:
      # 从环境变量获取API密钥，或在生产环境中配置
      api_key: "${BINANCE_API_KEY:-test_api_key}"
      api_secret: "${BINANCE_API_SECRET:-test_api_secret}"
      testnet: true  # 使用测试网，生产环境设为false
      base_url_http: null  # 使用默认URL
      base_url_ws: null    # 使用默认URL
  
  # 执行客户端配置
  exec_clients:
    binance:
      api_key: "${BINANCE_API_KEY:-test_api_key}"
      api_secret: "${BINANCE_API_SECRET:-test_api_secret}"
      testnet: true  # 使用测试网，生产环境设为false
      base_url_http: null  # 使用默认URL