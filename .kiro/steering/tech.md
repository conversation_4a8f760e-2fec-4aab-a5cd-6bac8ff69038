# Technology Stack

## Core Framework
- **Nautilus Trader**: Primary trading framework (>=1.190.0)
- **Python**: 3.11+ required, supports 3.9, 3.10, 3.11
- **ONNX Runtime**: AI model inference (>=1.15.0)

## AI/ML Stack
- **ONNX**: Primary model format (recommended over Pickle)
- **NumPy**: Numerical computing (>=1.24.0)
- **Pandas**: Data manipulation (>=2.0.0)
- **scikit-learn**: ML utilities (>=1.3.0)

## Data & Caching
- **Redis**: Primary caching layer (>=4.5.0)
- **SQLite**: Local data persistence (Python built-in)
- **PyYAML**: Configuration management (>=6.0)

## Development Tools
- **pytest**: Testing framework with asyncio support
- **black**: Code formatting
- **flake8**: Linting
- **mypy**: Type checking
- **structlog**: Structured logging

## Common Commands

### Environment Setup
```bash
# Create and activate virtual environment
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# Install dependencies
pip install -r requirements.txt
pip install -e .[dev]  # Development dependencies
```

### Development Workflow
```bash
# Code formatting
black src/ tests/

# Linting
flake8 src/ tests/

# Type checking
mypy src/

# Run tests
pytest                    # All tests
pytest tests/unit/        # Unit tests only
pytest tests/integration/ # Integration tests only
pytest --cov=ai_trading --cov-report=html  # With coverage
```

### Running the System
```bash
# Development mode
python -m ai_trading.main --config config/ai_trading_config.yaml

# Production mode (via console script)
ai-trading --config config/ai_trading_config.yaml --mode production
```

## Performance Requirements
- **Mainstream coins**: ≤100ms latency
- **AI inference**: ≤50ms latency
- **Other coins**: ≤300ms latency

## Configuration
- Primary config: `config/ai_trading_config.yaml`
- Environment variables via `.env` files
- Model paths and versions in YAML config