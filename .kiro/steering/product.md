# Product Overview

AI Trading System is a sophisticated cryptocurrency short-term trading platform built on the Nautilus Trader framework. The system employs a dual-AI architecture combining BTC prediction models with contract trading decision algorithms.

## Core Features

- **Dual AI Architecture**: BTC prediction AI + contract trading decision AI
- **Dynamic Leverage Management**: ATR volatility-based intelligent leverage adjustment
- **Multi-layer Risk Control**: Real-time risk monitoring, circuit breaker mechanisms, and degradation strategies
- **Progressive Development**: 6 independent runnable development phases
- **High Performance**: ≤100ms latency requirement for mainstream cryptocurrencies
- **Real Data Driven**: Strictly uses real interfaces, prohibits simulated data

## Target Market

Cryptocurrency short-term trading with focus on:
- Mainstream cryptocurrency pairs
- Contract/futures trading
- High-frequency decision making
- Risk-managed automated trading

## Development Philosophy

The system follows a progressive development approach with 6 distinct phases, ensuring each stage is independently runnable and testable. All development must use real trading interfaces and data - no simulation or mock data allowed.