<!------------------------------------------------------------------------------------
1.全中文对话，我是windows11系统，如果你需要，在调用Nautilus Trader框架代码时使用context7获取最新文档
2.所有的需求都需要完整的实现，禁止简单实现，每次实现完一个需求需要仔细检查是否有遗漏的实现
3.代码需要有详细的中文注释
4.代码需要高拓展，高可用
5.重要，每个需求最后都需要完整运行集成测试通过，仔细检查测试的日志是正确的输出，无报错
6.可以依赖模块，但是出现报错需要解决，也可以pip 安装新模块
7.所有参数设置都需要以实际使用为准，测试类的参数必须不能影响生产实际的。 如果有影响我们就以生产实际的为准
8.模块的测试类出现问题最好直接修改这个文件，太多测试类后续不容易管理
-------------------------------------------------------------------------------------> 