# Project Structure

## Directory Organization

```
ai-trading-system/
├── src/ai_trading/          # Main source code
│   ├── core/               # Core system modules
│   ├── models/             # AI model interfaces and loaders
│   ├── strategies/         # Trading strategy implementations
│   ├── risk/               # Risk management modules
│   ├── data/               # Data processing and caching
│   └── __init__.py
├── models/                 # AI model files storage
│   ├── btc_predictor/      # BTC prediction models
│   ├── trading_ai/         # Trading decision models
│   └── backup/             # Model version backups
├── config/                 # Configuration files
│   └── ai_trading_config.yaml
├── tests/                  # Test suite
│   ├── unit/               # Unit tests
│   ├── integration/        # Integration tests
│   └── __init__.py
├── scripts/                # Utility scripts
│   └── setup_env.py        # Environment setup
├── data/                   # Runtime data storage
├── docs/                   # Documentation
└── .kiro/                  # Kiro IDE configuration
    ├── steering/           # AI assistant guidance
    └── specs/              # Feature specifications
```

## Code Organization Principles

### Source Code (`src/ai_trading/`)
- **core/**: System initialization, configuration loading, main entry points
- **models/**: AI model interfaces, ONNX/Pickle loaders, model management
- **strategies/**: Nautilus trading strategies, trend following, grid trading
- **risk/**: Risk management, circuit breakers, position sizing, leverage control
- **data/**: Redis/SQLite interfaces, data caching, real-time data processing

### Model Management (`models/`)
- Each model type has its own subdirectory
- ONNX format preferred over Pickle
- `model_info.json` for version tracking
- Automatic backup before updates

### Testing Structure (`tests/`)
- **unit/**: Fast, isolated component tests
- **integration/**: End-to-end system tests
- Mirror source structure in test organization
- Use pytest with asyncio support

## Naming Conventions

### Files and Modules
- Snake_case for Python files: `btc_predictor.py`
- Package names: lowercase, no underscores
- Test files: `test_<module_name>.py`

### Classes and Functions
- PascalCase for classes: `BTCPredictor`, `RiskManager`
- Snake_case for functions and variables: `load_model()`, `current_price`
- Constants: UPPER_SNAKE_CASE: `MAX_LEVERAGE`, `DEFAULT_TIMEOUT`

### Configuration
- YAML for main configuration
- Environment variables for secrets
- Model paths relative to project root

## Import Structure
- Absolute imports from `ai_trading` package
- Group imports: stdlib, third-party, local
- Use `from ai_trading.core import config` style

## Development Phases
The codebase supports 6 progressive development phases:
1. Basic framework setup
2. Data layer implementation  
3. AI model integration
4. Nautilus strategy integration
5. Risk management completion
6. Production optimization

Each phase should be independently runnable and testable.