# 实现计划

- [ ] 阶段1：基础框架搭建

  - [x] 1.1 创建项目目录结构和基础配置
    - 创建标准Python项目结构（src/、tests/、config/等）
    - 设置Nautilus Trader依赖和虚拟环境
    - 创建基础的setup.py和requirements.txt
    - _需求: 需求1.1, 需求4.1_

  - [x] 1.2 实现Nautilus TradingNode初始化模块
    - 创建TradingNodeConfig配置类
    - 实现TradingNode初始化和构建逻辑
    - 添加基础的日志配置和错误处理
    - 验证节点能连接交易所测试网
    - 编写单元测试验证节点能够正常启动
    - _需求: 需求3.1, 需求4.1_

  - [x] 1.3 创建AI模型接口基类







    - 定义AIModelInterface抽象基类
    - 实现模型加载、预测、信息获取的接口方法
    - 创建PredictionResult数据类
    - 添加模型文件路径配置支持
    - _需求: 需求1.1, 需求2.1_

  - [x] 1.4 实现配置管理系统





    - 创建基于YAML的配置文件结构
    - 实现配置加载和验证逻辑
    - 支持Asia/Shanghai时区配置
    - 添加配置热重载机制
    - _需求: 需求4.1_

- [ ] 阶段2：数据层实现

  - [x] 2.1 实现Redis缓存系统（严格遵循Nautilus数据格式）





    - 创建CacheInterface抽象基类
    - 实现Redis客户端连接和基础操作
    - 强制存储Nautilus Bar对象序列化格式，禁止自定义数据结构
    - 实现BTC预测结果的存储和检索（键名btc_pred:latest）
    - _需求: 需求1.3, 需求4.2_

  - [x] 2.2 实现SQLite本地缓存作为备份




    - 创建SQLite数据库表结构
    - 实现与Redis相同的接口
    - 添加数据同步和备份机制
    - 编写缓存故障切换逻辑
    - _需求: 需求4.3_

  - [x] 2.3 集成Nautilus DataEngine进行数据获取测试







    - 强制使用DataEngine.request_bars()获取真实历史数据
    - 禁止使用其他数据源（如直接API调用或自生成数据）
    - 验证数据格式与缓存系统的兼容性
    - 实现数据预处理和标准化
    - 编写端到端数据流测试
    - _需求: 需求1.2, 需求2.2_

- [ ] 阶段3：AI模型集成

  - [x] 3.1 实现BTC预测AI模块






    - 创建BTCPredictor类继承AIModelInterface
    - 实现ONNX/Pickle模型加载功能
    - 使用Nautilus DataEngine获取BTC历史数据作为输入
    - 实现每小时预测调度（Asia/Shanghai时区）
    - _需求: 需求1.1, 需求1.2_

  - [ ] 3.2 实现交易决策AI模块
    - 创建TradingAI类继承AIModelInterface
    - 实现实时推理功能（延迟≤100ms主流币种）
    - 集成BTC预测结果作为辅助输入
    - 实现信号增强算法：final_confidence = coin_confidence * (1 + 0.3 * btc_confidence)
    - _需求: 需求2.1, 需求2.3_

  - [ ] 3.3 实现模型热更新机制
    - 监控模型文件变化
    - 实现模型版本验证和兼容性检查
    - 添加模型切换和回滚功能
    - 编写模型更新失败的错误处理
    - _需求: 需求4.5_

  - [ ] 3.4 使用Nautilus BacktestEngine验证AI模型
    - 创建简单的测试策略类
    - 强制使用BacktestEngine运行历史数据回测，禁止使用自定义回测框架
    - 验证AI模型在Nautilus环境中的兼容性
    - 记录和分析回测结果
    - _需求: 需求5.1_

- [ ] 阶段4：Nautilus策略集成

  - [ ] 4.1 创建AI策略基类（继承Nautilus Strategy）
    - 继承nautilus_trader.core.strategy.Strategy
    - 实现on_start()方法初始化Nautilus组件
    - 实现on_bar()方法处理市场数据
    - 通过self.cache.account()获取账户信息
    - 集成AI模型调用和Redis缓存读取
    - _需求: 需求3.1, 需求3.2_

  - [ ] 4.2 实现趋势跟踪策略
    - 创建TrendFollowingStrategy继承AIStrategyBase
    - 实现BTC方向与币种趋势一致时的信号增强
    - 使用金字塔加仓逻辑（首仓6%，总仓位≤24%）
    - 强制通过ExecutionEngine.submit_order()执行所有订单
    - 禁止绕过Nautilus订单管理系统
    - _需求: 需求2.3, 需求2.5_

  - [ ] 4.3 实现网格套利策略
    - 创建GridTradingStrategy继承AIStrategyBase
    - 实现BTC震荡时的网格参数优化：spacing *= 0.8, levels *= 1.2
    - 添加网格订单管理和动态调整
    - 强制通过ExecutionEngine.submit_order()执行所有网格订单
    - 集成止损和风控机制
    - _需求: 需求2.4_

  - [ ] 4.4 集成Nautilus风控检查
    - 在策略中集成RiskEngine预检查
    - 实现单笔风险≤2%、敞口≤15%的验证
    - 记录风控拦截的详细原因和订单信息
    - 添加风控拦截日志记录
    - 编写风控规则触发的测试用例
    - _需求: 需求3.5, 需求3.6_

- [ ]  阶段5：风险管理完善

  - [ ] 5.1 实现动态杠杆计算器
    - 创建DynamicLeverageCalculator类
    - 实现ATR标准化计算：normalized_atr = atr / close_price * 100
    - 实现杠杆公式：target_leverage = max(1, min(10, 2 / volatility_ratio))
    - 添加调整阈值检查：杠杆变化>0.5时才调整
    - 集成到策略的订单生成流程
    - _需求: 需求3.6, 需求3.7_

  - [ ] 5.2 创建风险检查器（强制继承RiskEngine）
    - 强制继承nautilus_trader.risk.engine.RiskEngine基类
    - 实现动态杠杆的订单预检查
    - 添加实时风险指标监控
    - 实现风控规则违反时的订单拦截
    - 禁止创建独立的风控系统，必须集成到Nautilus风控体系
    - _需求: 需求3.5, 需求3.8_

  - [ ] 5.3 实现市场异常检测和熔断机制
    - 创建MarketAnomalyDetector类
    - 实现分级熔断：波动率>3%降杠杆至5x，>5%禁用AI切换规则引擎，>7%仅允许平仓
    - 当波动率>5%时自动禁用AI策略
    - 添加单日亏损5%的熔断触发
    - 实现规则引擎备用策略（海龟止损）
    - _需求: 需求6.1, 需求6.2, 需求6.3_

  - [ ] 5.4 实现降级策略和故障恢复
    - 创建多层降级机制：AI→规则引擎→仅止损
    - 实现AI推理超时的自动切换
    - 添加系统恢复条件检测
    - 编写故障注入测试验证降级功能
    - _需求: 需求6.4, 需求6.5_

- [ ] 阶段6：生产优化

  - [ ] 6.1 性能优化和延迟控制
    - 优化AI模型推理性能（目标≤50ms）
    - 实现分层延迟标准（主流币种≤100ms，其他≤300ms）
    - 添加性能瓶颈监控和告警
    - 使用连接池优化Redis和数据库连接
    - _需求: 需求2.6, 需求5.3_

  - [ ] 6.2 集成Nautilus PerformanceMonitor监控系统
    - 强制使用nautilus_trader.monitoring.performance.PerformanceMonitor
    - 记录AI推理延迟、交易成功率、盈亏指标
    - 监控订单执行延迟（<100ms达标率）
    - 实现实时监控面板和告警机制
    - 添加历史性能数据分析和导出
    - _需求: 需求5.1, 需求5.2_

  - [ ] 6.3 实现AI决策日志和审计系统
    - 创建结构化日志记录系统
    - 记录AI推理输入、输出、置信度、执行时间
    - 实现BTC预测与交易决策冲突的检测和记录
    - 添加日志查询API和CSV导出功能
    - _需求: 需求5.1, 需求5.2, 需求5.4, 需求5.5_

  - [ ] 6.4 生产部署和运维准备
    - 创建Docker容器化配置
    - 编写部署脚本和健康检查
    - 实现配置管理和环境变量支持
    - 编写运维文档和故障排查指南
    - _需求: 需求4.4_

  - [ ] 6.5 压力测试和稳定性验证
    - 使用真实生产级数据流量进行压力测试
    - 验证系统在高并发下的稳定性
    - 测试连续运行24小时以上的可靠性
    - 验证所有降级和恢复机制的有效性
    - _需求: 需求6.4, 需求6.5_

## 关键任务验证清单

### Nautilus集成规范验证

| 任务 | 验证重点 | 验证方法 | 禁止行为 |
|------|----------|----------|----------|
| **1.2** | TradingNode初始化 | 启动节点并连接交易所测试网 | 绕过TradingNode直接初始化组件 |
| **2.3** | 数据获取规范 | 检查代码是否仅使用DataEngine.request_bars() | 直接调用交易所API或自生成数据 |
| **3.4** | 回测集成 | 使用BacktestEngine运行策略 | 使用自定义回测框架 |
| **4.2/4.3** | 订单执行 | 检查是否使用ExecutionEngine.submit_order() | 绕过Nautilus订单管理系统 |
| **5.2** | 风控继承 | 验证类继承RiskEngine | 创建独立风控系统 |
| **6.2** | 监控集成 | 使用PerformanceMonitor记录指标 | 使用第三方监控系统 |

### 高风险任务特别说明

#### 动态杠杆计算（5.1）
```python
# 必须实现的标准化ATR计算
normalized_atr = atr / close_price * 100  # 百分比表示
volatility_ratio = normalized_atr / avg_normalized_atr

# 杠杆调整阈值检查
if abs(new_leverage - current_leverage) > 0.5:  # 杠杆变化>0.5才调整
    update_leverage(new_leverage)
```

#### 网格策略参数优化（4.3）
```python
# 明确的参数调整公式
if btc_trend == "sideways" and btc_confidence > 0.7:
    new_spacing = base_spacing * 0.8   # 间距缩小20%
    new_levels = int(base_levels * 1.2)    # 层级增加20%
```

#### 分级熔断机制（5.3）
```mermaid
graph TD
    A[波动率监控] --> B{波动率>3%?}
    B -->|是| C[降低杠杆至5x]
    B -->|否| A
    C --> D{波动率>5%?}
    D -->|是| E[禁用AI，切换规则引擎]
    D -->|否| A
    E --> F{波动率>7%?}
    F -->|是| G[仅允许平仓操作]
    F -->|否| A
```

### 最终交付验证标准

1. **数据获取**：所有市场数据必须通过`DataEngine.request_bars()`获取
2. **订单执行**：所有交易必须通过`ExecutionEngine.submit_order()`提交
3. **账户信息**：持仓/余额必须通过`self.cache.account()`获取
4. **风险控制**：必须继承并扩展`RiskEngine`基类
5. **回测验证**：必须使用`BacktestEngine`进行策略验证
6. **策略基类**：必须继承`nautilus_trader.core.strategy.Strategy`
7. **节点初始化**：必须使用`TradingNode`进行系统初始化
8. **监控系统**：必须使用`PerformanceMonitor`记录性能指标