# 设计文档

## 概述

基于Nautilus Trader平台的AI驱动加密货币短线交易系统设计。系统采用模块化架构，支持渐进式开发，每个开发阶段都能独立运行和验证。核心设计原则：高内聚低耦合、可测试性、可扩展性。

## 架构设计

### 系统整体架构

```mermaid
graph TB
    subgraph "AI决策层"
        A1[BTC预测AI模块]
        A2[交易决策AI模块]
    end
    
    subgraph "数据层"
        D1[Redis缓存]
        D2[SQLite本地缓存]
        D3[配置管理器]
    end
    
    subgraph "策略层"
        S1[AI策略基类]
        S2[趋势跟踪策略]
        S3[网格套利策略]
    end
    
    subgraph "风控层"
        R1[动态杠杆计算器]
        R2[风险检查器]
        R3[熔断机制]
    end
    
    subgraph "Nautilus Trader平台"
        N1[数据引擎]
        N2[风险引擎]
        N3[执行引擎]
        N4[账户管理]
    end
    
    A1 --> D1
    A2 --> S1
    D1 --> S1
    S1 --> S2
    S1 --> S3
    S2 --> R1
    S3 --> R1
    R1 --> R2
    R2 --> N2
    N1 --> A1
    N1 --> A2
    N2 --> N3
    N3 --> N4
```

### 核心组件设计

#### 1. AI决策层

**BTC预测AI模块**
- **职责**：每小时预测BTC未来走势
- **输入**：24小时OHLCV数据
- **输出**：方向预测（涨/跌/震荡）+ 置信度
- **存储**：Redis + 本地SQLite备份

**交易决策AI模块**
- **职责**：实时生成交易信号
- **输入**：目标币种数据 + BTC方向预测
- **输出**：交易信号（买入/卖出/持有）+ 置信度
- **延迟要求**：主流币种≤100ms，其他币种≤300ms

#### 2. 数据层

**Redis缓存**
- **BTC预测结果**：键名`btc_pred:latest`，TTL 65分钟
- **AI模型状态**：模型版本、加载状态、性能指标
- **实时指标**：ATR、波动率、杠杆倍数

**SQLite本地缓存**
- **备用存储**：Redis故障时的降级方案
- **历史数据**：AI决策日志、性能统计
- **配置备份**：关键配置参数的本地副本

#### 3. 策略层

**AI策略基类**
```python
from nautilus_trader.core.strategy import Strategy
from nautilus_trader.execution.engine import ExecutionEngine

class AIStrategyBase(Strategy):
    def __init__(self, config):
        super().__init__(config)
        self.btc_predictor = BTCPredictor()
        self.trade_ai = TradingAI()
        self.risk_manager = DynamicRiskManager()
    
    def on_start(self):
        """策略启动时初始化Nautilus组件"""
        self.data_engine = self.kernel.data_engine
        self.exec_engine = self.kernel.exec_engine
        self.cache = self.kernel.cache
    
    def on_bar(self, bar: Bar):
        """接收Nautilus Bar数据进行AI决策"""
        # 通过正规接口获取BTC数据
        btc_bars = self.data_engine.request_bars(
            bar_type=BarType.from_str("BTC/USDT-1-MINUTE-LAST-EXTERNAL"),
            count=24 * 60
        )
        
        btc_signal = self.get_btc_signal(btc_bars)
        trade_signal = self.generate_trade_signal(bar, btc_signal)
        validated_signal = self.validate_risk(trade_signal)
        self.execute_trade(validated_signal)
    
    def execute_trade(self, signal: TradeSignal):
        """通过Nautilus ExecutionEngine执行交易"""
        if signal.action != "HOLD":
            order = self.create_order(signal)
            self.exec_engine.submit_order(order)
```

#### 4. 风控层

**动态杠杆计算器**
- **ATR计算**：14周期ATR标准化
- **杠杆公式**：`target_leverage = max(1, min(10, 2 / volatility_ratio))`
- **调整阈值**：波动率变化>10%时才调整

**风险检查器**
- **单笔风险**：≤账户资金2%
- **敞口限制**：单币种≤总资产15%
- **杠杆上限**：动态调整，最高10倍

## 组件和接口

### 核心接口定义

#### AI模型接口
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class PredictionResult:
    direction: str  # "bullish", "bearish", "sideways"
    confidence: float  # 0.0 - 1.0
    timestamp: str  # ISO 8601 with timezone
    metadata: Dict[str, Any]

class AIModelInterface(ABC):
    @abstractmethod
    def load_model(self, model_path: str) -> bool:
        """加载AI模型"""
        pass
    
    @abstractmethod
    def predict(self, data: Dict[str, Any]) -> PredictionResult:
        """生成预测结果"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass
```

#### 缓存接口
```python
class CacheInterface(ABC):
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查缓存是否可用"""
        pass
```

#### 风险管理接口
```python
@dataclass
class RiskCheckResult:
    approved: bool
    adjusted_size: float
    leverage: float
    reason: str

class RiskManagerInterface(ABC):
    @abstractmethod
    def check_trade_risk(self, signal: TradeSignal) -> RiskCheckResult:
        """检查交易风险"""
        pass
    
    @abstractmethod
    def calculate_dynamic_leverage(self, symbol: str) -> float:
        """计算动态杠杆"""
        pass
```

### 配置管理

#### 配置文件结构
```yaml
# config/ai_trading_config.yaml
system:
  timezone: "Asia/Shanghai"
  log_level: "INFO"

ai_models:
  btc_predictor:
    model_path: "/models/btc_predictor.onnx"
    version: "v1.0.0"
    update_interval: 3600  # 1小时
  
  trading_ai:
    model_path: "/models/trading_ai.onnx"
    version: "v1.0.0"
    inference_timeout: 100  # 毫秒

redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null
  connection_timeout: 5

risk_management:
  max_trade_risk: 0.02
  exposure_limit: 0.15
  max_leverage: 10
  volatility_threshold: 0.1
  circuit_breaker:
    daily_loss_limit: 0.05
    volatility_limit: 0.05

strategies:
  trend_following:
    enabled: true
    confidence_threshold: 0.6
    btc_weight: 0.3
  
  grid_trading:
    enabled: true
    base_spacing: 0.005
    max_levels: 10
    btc_sideways_threshold: 0.7
```

## 数据模型

### 核心数据结构

#### BTC预测结果
```python
@dataclass
class BTCPrediction:
    direction: str  # "bullish", "bearish", "sideways"
    confidence: float
    timestamp: datetime
    valid_until: datetime
    metadata: Dict[str, Any]
    
    def to_redis_dict(self) -> Dict[str, str]:
        return {
            "direction": self.direction,
            "confidence": str(self.confidence),
            "timestamp": self.timestamp.isoformat(),
            "valid_until": self.valid_until.isoformat(),
            "metadata": json.dumps(self.metadata)
        }
```

#### 交易信号
```python
@dataclass
class TradeSignal:
    symbol: str
    action: str  # "BUY", "SELL", "HOLD"
    confidence: float
    size: float
    leverage: float
    btc_influence: float
    timestamp: datetime
    strategy_type: str  # "trend", "grid"
    metadata: Dict[str, Any]
```

#### 风险指标
```python
@dataclass
class RiskMetrics:
    symbol: str
    atr: float
    volatility_ratio: float
    current_leverage: float
    recommended_leverage: float
    position_size: float
    risk_percentage: float
    timestamp: datetime
```

## 错误处理

### 错误分类和处理策略

#### 1. AI模型错误
- **模型加载失败**：使用备用模型或进入安全模式
- **推理超时**：切换到规则引擎
- **预测结果异常**：记录日志并使用历史预测

#### 2. 数据层错误
- **Redis连接失败**：自动切换到SQLite本地缓存
- **数据格式错误**：数据清洗和验证
- **缓存过期**：使用最近一次有效数据

#### 3. 交易执行错误
- **订单被拒**：调整订单参数重试
- **网络延迟**：启用备用交易所
- **余额不足**：调整仓位大小

### 降级策略

```mermaid
graph TD
    A[正常AI交易] --> B{AI推理失败?}
    B -->|是| C[规则引擎交易]
    B -->|否| D[继续AI交易]
    
    C --> E{规则引擎失败?}
    E -->|是| F[仅止损平仓]
    E -->|否| G[继续规则交易]
    
    F --> H[人工干预]
    
    D --> I{延迟超标?}
    I -->|是| C
    I -->|否| A
```

## 测试策略

### 测试层级

#### 1. 单元测试（使用真实接口数据）
- **AI模型测试**：使用真实历史OHLCV数据测试模型加载和预测
- **缓存测试**：连接真实Redis实例测试读写和故障切换
- **风控测试**：使用真实账户数据测试杠杆计算和风险检查

#### 2. 集成测试（使用Nautilus真实接口）
- **端到端流程**：通过Nautilus完整的数据→策略→执行流程测试
- **Nautilus集成**：使用Nautilus DataEngine、ExecutionEngine、Portfolio等真实组件
- **故障恢复**：在Nautilus环境中测试各组件故障时的降级和恢复
- **性能测试**：使用Nautilus提供的真实市场数据流测试延迟和吞吐量

#### 3. 回测验证（使用真实历史数据）
- **历史数据回测**：使用Nautilus回测引擎和真实2021-2023年行情数据
- **策略对比**：在相同真实数据集上对比AI策略和传统策略
- **风险评估**：基于真实交易记录计算最大回撤、夏普比率、胜率

#### 4. 测试数据获取规范
```python
# 正确的测试数据获取方式 - 完全使用Nautilus接口
from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.trading.node import TradingNode
from nautilus_trader.backtest.engine import BacktestEngine

class TestDataProvider:
    def setup_test_environment(self):
        # 使用Nautilus TradingNode初始化
        config = TradingNodeConfig(
            log_level="INFO",
            trader_id="AI_TRADER_TEST",
        )
        self.node = TradingNode(config=config)
        self.node.build()
        
        self.data_engine = self.node.kernel.data_engine
        self.cache = self.node.kernel.cache
        self.redis_client = redis.Redis(host="test-redis", port=6379)
    
    def get_test_data(self, symbol: str, days: int = 30):
        # 通过Nautilus DataEngine获取真实历史数据
        bar_type = BarType.from_str(f"{symbol}-1-MINUTE-LAST-EXTERNAL")
        return self.data_engine.request_bars(
            bar_type=bar_type,
            count=days * 24 * 60
        )
    
    def get_account_data(self, account_id: AccountId):
        # 通过Nautilus Cache获取真实账户数据
        return self.cache.account(account_id)
    
    def run_backtest(self, strategy_class):
        # 使用Nautilus BacktestEngine验证策略
        backtest = BacktestEngine()
        backtest.add_strategy(strategy_class)
        return backtest.run()

# 严格禁止的做法：
# 1. 自生成模拟数据 ❌
# 2. 直连交易所API ❌ 
# 3. 绕过Nautilus接口 ❌
# 4. 不使用TradingNode初始化 ❌
```

### 测试数据准备

#### 真实数据接口集成
**重要原则**：所有测试必须使用真实接口数据，禁止自生成模拟数据以避免系统集成问题。

```python
from nautilus_trader.adapters.binance import BinanceDataClient
from nautilus_trader.execution.engine import ExecutionEngine
from nautilus_trader.core.data import BarType

class RealDataProvider:
    def __init__(self, data_engine, cache):
        self._data_engine = data_engine
        self._cache = cache
    
    def get_nautilus_bars(self, symbol: str) -> List[Bar]:
        """通过Nautilus DataEngine获取历史K线数据"""
        bar_type = BarType.from_str(f"{symbol}-1-MINUTE-LAST-EXTERNAL")
        return self._data_engine.request_bars(
            bar_type=bar_type,
            count=1440  # 24小时数据
        )
    
    def get_nautilus_account_info(self, account_id: AccountId) -> Account:
        """通过Nautilus Cache获取账户信息"""
        return self._cache.account(account_id)
    
    def get_redis_cached_data(self, key: str) -> Optional[Any]:
        """从Redis获取真实缓存数据"""
        return self.redis_client.get(key)
```

#### 测试环境数据源
- **历史数据**：使用Nautilus DataEngine获取真实历史K线和tick数据
- **实时数据**：通过Nautilus数据流获取真实实时市场数据
- **账户数据**：使用Nautilus Portfolio和Account接口获取真实账户信息
- **订单执行**：通过Nautilus ExecutionEngine执行真实订单（测试网环境）
- **缓存数据**：使用真实Redis实例进行缓存测试
- **配置数据**：使用真实Nautilus配置文件和环境变量

## 渐进式开发计划

### 阶段1：基础框架搭建（可独立运行）
- **目标**：建立项目结构和Nautilus Trader初始化
- **交付物**：
  - 项目目录结构
  - Nautilus TradingNode初始化模块
  - 配置管理系统（基于TradingNodeConfig）
  - 基础日志系统
  - 真实AI模型接口（连接预训练模型文件）
- **验证方式**：能够启动Nautilus节点并加载配置、加载真实AI模型文件
- **测试数据**：使用真实的Nautilus配置文件和模型文件路径
- **关键修正**：必须包含TradingNode初始化，确保所有后续阶段都基于Nautilus框架

### 阶段2：数据层实现（可独立运行）
- **目标**：实现Redis缓存系统，遵循Nautilus数据格式规范
- **交付物**：
  - Redis缓存实现（存储Nautilus Bar对象序列化数据）
  - SQLite本地缓存
  - 缓存故障切换机制
  - 数据持久化和恢复
- **验证方式**：使用Nautilus DataClient测试数据获取，Redis存储Nautilus标准格式
- **测试数据**：通过Nautilus DataEngine获取真实市场数据并缓存
- **关键修正**：数据格式必须遵循Nautilus Bar规范，确保与回测引擎兼容

### 阶段3：AI模型集成（可独立运行）
- **目标**：集成真实AI模型，输入使用Nautilus DataEngine输出
- **交付物**：
  - BTC预测AI模块（输入Nautilus Bar数据）
  - 交易决策AI模块
  - 模型热更新机制
  - 性能监控
- **验证方式**：在Nautilus回测环境运行模型，使用BacktestEngine验证
- **测试数据**：通过DataEngine.request_bars()获取真实BTC历史数据
- **关键修正**：模型输入必须使用DataEngine输出，确保数据格式一致性

### 阶段4：Nautilus策略集成（可独立运行）
- **目标**：将AI决策集成到Nautilus策略框架，策略继承Strategy基类
- **交付物**：
  - AI策略基类（继承nautilus_trader.core.strategy.Strategy）
  - 趋势跟踪策略实现
  - 基础风控集成
- **验证方式**：在交易所测试网执行真实订单，订单通过ExecutionEngine提交
- **测试数据**：使用Nautilus DataEngine提供的真实市场数据流和Cache提供的账户信息
- **关键修正**：策略必须继承Strategy基类，订单提交通过ExecutionEngine

### 阶段5：风险管理完善（可独立运行）
- **目标**：实现完整的风险管理系统，集成Nautilus RiskEngine
- **交付物**：
  - 动态杠杆计算器
  - 风险检查器（继承并扩展RiskEngine）
  - 熔断机制
  - 降级策略
- **验证方式**：构造风控规则触发测试用例，验证订单拦截功能
- **测试数据**：通过Cache.account()获取真实账户信息，DataEngine获取波动率数据
- **关键修正**：风险检查必须继承RiskEngine基类，集成到Nautilus风控体系

### 阶段6：生产优化（可独立运行）
- **目标**：性能优化和生产部署准备，集成Nautilus原生监控
- **交付物**：
  - 性能优化
  - 监控告警系统（集成PerformanceMonitor）
  - 部署脚本
  - 运维文档
- **验证方式**：生产环境压力测试，连续稳定运行24小时以上
- **测试数据**：使用Nautilus提供的生产级别数据流量进行压力测试
- **关键修正**：监控必须使用Nautilus原生PerformanceMonitor，确保指标一致性

每个阶段都包含完整的测试用例和文档，确保系统在任何阶段都能独立运行和验证功能。
## Nautilu
s集成最佳实践

### 数据获取黄金法则

```mermaid
graph LR
    A[交易所] --> B(Nautilus DataClient)
    B --> C(DataEngine)
    C --> D[策略层]
    D --> E[AI模型]
```

所有市场数据必须通过这个标准流程获取，确保数据格式一致性和回测兼容性。

### 订单执行规范流程

```python
from nautilus_trader.execution.orders import OrderFactory
from nautilus_trader.model.enums import OrderSide

# 完整订单生命周期管理
order = OrderFactory.limit(
    instrument=BTC_USDT,
    price=29500.0,
    quantity=0.1,
    side=OrderSide.BUY
)

# 通过正规通道提交
self.exec_engine.submit_order(order)

# 事件监听
def on_order_filled(self, event):
    self.portfolio.update(event)
```

### 风险管理集成

```python
from nautilus_trader.risk.engine import RiskEngine

class DynamicRiskManager(RiskEngine):
    def check_order(self, order: Order) -> bool:
        leverage = self.calc_leverage(order.instrument_id.symbol)
        if leverage > self.config.max_leverage:
            self.log.warning(f"订单被拦截：杠杆{leverage}超过限制")
            return False  # 拦截订单
        return True
```

### 最终验证清单

✅ **数据获取**：所有市场数据必须通过`DataEngine.request_bars()`获取  
✅ **订单执行**：所有交易必须通过`ExecutionEngine.submit_order()`提交  
✅ **账户信息**：持仓/余额必须通过`Cache.account()`获取  
✅ **风险控制**：必须继承并扩展`RiskEngine`基类  
✅ **回测验证**：必须使用`BacktestEngine`进行策略验证  
✅ **策略基类**：必须继承`nautilus_trader.core.strategy.Strategy`  
✅ **节点初始化**：必须使用`TradingNode`进行系统初始化