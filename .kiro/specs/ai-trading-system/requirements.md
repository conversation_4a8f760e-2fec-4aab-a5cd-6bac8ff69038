# 需求文档

## 介绍

基于Nautilus Trader平台构建的AI驱动加密货币短线交易系统。Nautilus Trader已提供完整的交易基础设施（风险管理、订单执行、数据处理、多交易所连接等），我们的重点是集成双AI决策层：一个AI专门预测BTC走势（作为市场大方向参考），另一个AI负责具体的交易决策（结合BTC走势预测和目标币种的实时数据）。系统需要将AI决策无缝集成到Nautilus Trader的策略框架中。

**全局时区规范**：所有时间相关操作均使用Asia/Shanghai时区（UTC+8），确保BTC预测调度、Redis存储、日志记录的时间戳一致性。

## 需求

### 需求1：BTC趋势预测AI集成

**用户故事：** 作为交易系统，我需要集成BTC趋势预测AI模型，以便每小时预测BTC未来一小时的走势方向，为其他币种的交易策略提供辅助参考。

#### 验收标准

1. 当系统启动时，系统应当从配置文件指定路径加载预训练的BTC趋势预测模型（支持ONNX/Pickle格式）
2. 当每小时整点时（Asia/Shanghai时区），系统应当从Nautilus数据模块收集BTC过去24小时的OHLCV数据并生成未来一小时走势预测（涨>+0.5%/跌<-0.5%/震荡±0.5%内）
3. 当BTC预测完成时，系统应当将预测结果存储到Redis键`btc_pred:latest`，时间戳采用ISO 8601格式含时区信息，TTL设为65分钟
4. 当BTC预测模型加载失败时，系统应当记录错误日志并在Redis中设置`btc_pred:status=unavailable`
### 需求2：实时交易决策AI集成

**用户故事：** 作为交易系统，我需要集成实时交易决策AI模型，以便执行具体的交易策略（如趋势跟踪、网格套利等），并参考Redis中的BTC方向预测来提高决策准确性。

#### 验收标准

1. 当系统启动时，系统应当从配置文件指定路径加载预训练的交易决策模型（支持ONNX/Pickle格式）
2. 当接收到目标币种市场数据时，系统应当从Redis读取`btc_pred:latest`作为辅助参考
3. 当执行趋势策略时，如果BTC方向与币种趋势一致，系统应当按公式增强信号：`final_confidence = coin_confidence * (1 + 0.3 * btc_confidence)`
4. 当执行网格套利时，如果BTC预测为震荡且置信度>0.7，系统应当将网格间距缩小20%并增加网格层级20%以提高交易频率
5. 当使用金字塔加仓时，系统应当首仓使用0.5%本金，盈利后逐级加仓（每级0.5%），总仓位不超过2%本金
5. 当BTC方向预测不可用或超时时，系统应当仅基于币种自身数据进行交易决策
6. 当生成交易信号时，系统应当满足分层延迟要求≤100ms

### 需求3：Nautilus Trader策略集成

**用户故事：** 作为AI交易系统，我需要将双AI决策逻辑集成到Nautilus Trader的策略框架中，以便利用其完整的交易基础设施。

#### 验收标准

1. 当系统启动时，系统应当创建继承自Nautilus Strategy基类的AI策略类
2. 当Nautilus接收到市场数据时，系统应当触发AI模型推理并生成交易信号
3. 当AI生成交易信号时，系统应当通过Nautilus的订单管理系统执行交易
4. 当需要访问账户信息时，系统应当使用Nautilus提供的Portfolio和Account接口
5. 当AI生成交易信号时，系统应当通过Nautilus RiskEngine执行风险检查：单笔风险≤账户资金2%，单币种敞口≤总资产15%
6. 当执行交易时，系统应当基于ATR波动率动态调整杠杆：`target_leverage = max(1, min(10, 2 / volatility_ratio))`
7. 当波动率变化小于10%时，系统应当保持当前杠杆不变以减少滑点成本
8. 当风控规则触发时，系统应当调整订单规模或拒绝执行，并记录风控干预日志

### 需求4：Redis缓存和AI模型配置管理

**用户故事：** 作为系统维护人员，我需要配置Redis连接和AI模型路径，以便实现BTC预测结果的缓存和模型的灵活更新。

#### 验收标准

1. 当系统启动时，系统应当从配置文件读取Redis连接信息、AI模型文件路径和版本号
2. 当BTC预测AI运行时，系统应当将预测结果以JSON格式存储到Redis，同时维护本地SQLite缓存作为备份
3. 当交易AI需要BTC方向时，系统应当优先从Redis读取，失败时从本地缓存读取最近一次有效预测
4. 当Redis连接失败时，系统应当自动切换到本地缓存模式并每30秒尝试重连Redis
5. 当检测到新的模型文件时，系统应当验证模型版本兼容性后进行热更新，失败时回滚到前一版本

### 需求5：AI决策日志和监控

**用户故事：** 作为系统管理员，我需要监控AI决策过程和性能指标，以便分析和优化AI交易策略。

#### 验收标准

1. 当AI模型进行推理时，系统应当记录结构化日志：输入数据哈希、推理时间、输出结果、置信度
2. 当BTC预测和交易决策发生冲突时，系统应当记录冲突详情并触发告警（如BTC预测跌但币种信号强烈看涨）
4. 当推理延迟连续3次超过阈值时，系统应当发送紧急告警
5. 当需要分析时，系统应当提供基于时间范围的AI决策历史查询API和CSV导出功能
### 需求6：
市场异常检测和熔断机制

**用户故事：** 作为交易系统，我需要检测市场异常波动并自动切换到降级模式，以便在极端行情下保护资金安全。

#### 验收标准

1. 当检测到市场波动率超过5%时，系统应当自动切换到降级模式（禁用AI决策，启用规则引擎如海龟止损策略）
2. 当波动率突增至2倍以上时，系统应当强制杠杆降至1倍并启动对冲单保护
3. 当市场异常时，系统应当禁用网格策略并仅保留趋势跟踪策略
3. 当单日累计亏损达到账户资金5%时，系统应当触发熔断机制停止所有AI交易
4. 当熔断触发时，系统应当发送紧急告警并等待人工干预恢复
5. 当市场恢复正常且波动率降至3%以下时，系统应当自动恢复正常交易模式