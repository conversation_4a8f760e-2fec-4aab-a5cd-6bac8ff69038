#!/usr/bin/env python3
"""
真实AI模型信息演示脚本

演示如何加载和查看真实ONNX模型的基本信息。
严格使用真实数据接口，不生成任何模拟数据。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from ai_trading.models import BTCPredictorModel, TradingAIModel
from ai_trading.core.config import get_config_manager


def check_model_files():
    """检查模型文件是否存在"""
    print("=== 检查模型文件 ===")
    
    btc_model_path = Path("models/btc_predictor/model.onnx")
    trading_model_path = Path("models/trading_ai/trading_ai.onnx")
    
    print(f"BTC预测模型: {btc_model_path}")
    print(f"  存在: {'✓' if btc_model_path.exists() else '✗'}")
    if btc_model_path.exists():
        size = btc_model_path.stat().st_size / 1024 / 1024
        print(f"  大小: {size:.2f} MB")
    
    print(f"交易决策模型: {trading_model_path}")
    print(f"  存在: {'✓' if trading_model_path.exists() else '✗'}")
    if trading_model_path.exists():
        size = trading_model_path.stat().st_size / 1024 / 1024
        print(f"  大小: {size:.2f} MB")
    
    return btc_model_path.exists() and trading_model_path.exists()


def demo_model_loading():
    """演示模型加载和信息获取"""
    print("\n=== 模型加载演示 ===")
    
    # 从配置文件加载配置
    try:
        config_manager = get_config_manager("config/ai_trading_config.yaml")
        config = config_manager.load_config()
        print("✓ 配置文件加载成功")
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False
    
    # 创建BTC预测模型
    btc_config = config.ai_models['btc_predictor']
    btc_model_dict = {
        'model_path': btc_config.model_path,
        'version': btc_config.version,
        'model_type': 'onnx'
    }
    btc_model = BTCPredictorModel(btc_model_dict)
    print(f"创建BTC预测模型: {btc_model}")
    
    # 创建交易决策模型
    trading_config = config.ai_models['trading_ai']
    trading_model_dict = {
        'model_path': trading_config.model_path,
        'version': trading_config.version,
        'model_type': 'onnx'
    }
    trading_model = TradingAIModel(trading_model_dict)
    print(f"创建交易决策模型: {trading_model}")
    
    # 加载BTC预测模型
    print(f"\n加载BTC预测模型...")
    btc_success = btc_model.load_model()
    if btc_success:
        print("✓ BTC模型加载成功")
        
        # 获取模型信息
        btc_info = btc_model.get_model_info()
        print(f"  名称: {btc_info.name}")
        print(f"  版本: {btc_info.version}")
        print(f"  类型: {btc_info.model_type}")
        print(f"  输入形状: {btc_info.input_shape}")
        print(f"  输出形状: {btc_info.output_shape}")
        print(f"  模型大小: {btc_info.model_size / 1024 / 1024:.2f} MB")
        print(f"  描述: {btc_info.description}")
    else:
        print("✗ BTC模型加载失败")
        return False
    
    # 加载交易决策模型
    print(f"\n加载交易决策模型...")
    trading_success = trading_model.load_model()
    if trading_success:
        print("✓ 交易决策模型加载成功")
        
        # 获取模型信息
        trading_info = trading_model.get_model_info()
        print(f"  名称: {trading_info.name}")
        print(f"  版本: {trading_info.version}")
        print(f"  类型: {trading_info.model_type}")
        print(f"  输入形状: {trading_info.input_shape}")
        print(f"  输出形状: {trading_info.output_shape}")
        print(f"  模型大小: {trading_info.model_size / 1024 / 1024:.2f} MB")
        print(f"  描述: {trading_info.description}")
    else:
        print("✗ 交易决策模型加载失败")
        return False
    
    return True


def demo_model_validation():
    """演示模型输入验证功能"""
    print("\n=== 输入验证演示 ===")
    
    btc_model = BTCPredictorModel()
    trading_model = TradingAIModel()
    
    # 测试无效输入类型
    print("测试无效输入类型:")
    invalid_inputs = ["string", 123, None, [1, 2, 3]]
    
    for invalid_input in invalid_inputs:
        btc_valid = btc_model.validate_input_data(invalid_input)
        trading_valid = trading_model.validate_input_data(invalid_input)
        print(f"  输入 {type(invalid_input).__name__}: BTC模型={'✓' if btc_valid else '✗'}, 交易模型={'✓' if trading_valid else '✗'}")
    
    # 测试缺少必需字段
    print("\n测试缺少必需字段:")
    incomplete_btc = {'ohlcv_data': []}
    incomplete_trading = {'market_data': []}
    
    btc_valid = btc_model.validate_input_data(incomplete_btc)
    trading_valid = trading_model.validate_input_data(incomplete_trading)
    
    print(f"  BTC模型缺少technical_indicators: {'✓' if btc_valid else '✗'}")
    print(f"  交易模型缺少btc_signal: {'✓' if trading_valid else '✗'}")


def demo_configuration_system():
    """演示配置系统"""
    print("\n=== 配置系统演示 ===")
    
    try:
        config_manager = get_config_manager("config/ai_trading_config.yaml")
        config = config_manager.load_config()
        
        print("系统配置:")
        print(f"  时区: {config.system.timezone}")
        print(f"  日志级别: {config.system.log_level}")
        print(f"  交易员ID: {config.system.trader_id}")
        
        print("\nAI模型配置:")
        for name, model_config in config.ai_models.items():
            print(f"  {name}:")
            print(f"    模型路径: {model_config.model_path}")
            print(f"    版本: {model_config.version}")
            print(f"    备份路径: {model_config.backup_path}")
        
        print("\n风险管理配置:")
        risk = config.risk_management
        print(f"  最大交易风险: {risk.max_trade_risk * 100}%")
        print(f"  敞口限制: {risk.exposure_limit * 100}%")
        print(f"  最大杠杆: {risk.max_leverage}x")
        
        print("\n性能监控配置:")
        monitoring = config.monitoring
        print(f"  主流币种延迟阈值: {monitoring.latency_thresholds['mainstream_coins']}ms")
        print(f"  AI推理延迟阈值: {monitoring.latency_thresholds['ai_inference']}ms")
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")


def main():
    """主函数"""
    print("真实AI模型信息演示程序")
    print("=" * 50)
    print("注意：本程序严格使用真实数据接口，不生成任何模拟数据")
    
    try:
        # 检查模型文件
        if not check_model_files():
            print("\n✗ 模型文件不存在，请确保以下文件存在:")
            print("  - models/btc_predictor/model.onnx")
            print("  - models/trading_ai/trading_ai.onnx")
            return 1
        
        # 演示模型加载
        if not demo_model_loading():
            print("\n✗ 模型加载失败")
            return 1
        
        # 演示输入验证
        demo_model_validation()
        
        # 演示配置系统
        demo_configuration_system()
        
        print("\n" + "=" * 50)
        print("✓ 所有演示完成！真实AI模型接口工作正常。")
        print("📋 模型已准备好接收真实的市场数据进行预测。")
        
    except Exception as e:
        print(f"\n✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())