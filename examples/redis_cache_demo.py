#!/usr/bin/env python3
"""
Redis缓存系统演示脚本

该脚本演示了Redis缓存系统的主要功能：
1. 基础缓存操作
2. BTC预测结果存储
3. Nautilus Bar对象序列化
4. 模型状态管理
5. 健康检查

运行前请确保Redis服务器正在运行。
"""

import sys
import os
import yaml
import logging
from datetime import datetime, timedelta, timezone
from typing import List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Nautilus Trader imports
from nautilus_trader.model.data import Bar, BarType, BarSpecification
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.enums import BarAggregation, PriceType
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.core.datetime import dt_to_unix_nanos

# 项目imports
from src.ai_trading.data import initialize_cache, get_cache, BTCPrediction


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('redis_cache_demo.log')
        ]
    )


def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'config', 'ai_trading_config.yaml')
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def create_sample_bars() -> List[Bar]:
    """创建示例Nautilus Bar对象"""
    bars = []
    instrument_id = InstrumentId.from_str("BTC/USDT.BINANCE")
    
    bar_spec = BarSpecification(
        step=1,
        aggregation=BarAggregation.MINUTE,
        price_type=PriceType.LAST
    )
    
    bar_type = BarType(
        instrument_id=instrument_id,
        bar_spec=bar_spec
    )
    
    base_time = dt_to_unix_nanos(datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc))
    
    for i in range(10):
        bar = Bar(
            bar_type=bar_type,
            open=Price.from_str(f"{50000 + i * 100}.00"),
            high=Price.from_str(f"{50200 + i * 100}.00"),
            low=Price.from_str(f"{49800 + i * 100}.00"),
            close=Price.from_str(f"{50100 + i * 100}.00"),
            volume=Quantity.from_str(f"{100 + i * 10}.0"),
            ts_event=base_time + i * 60_000_000_000,
            ts_init=base_time + i * 60_000_000_000
        )
        bars.append(bar)
    
    return bars


def create_sample_btc_prediction() -> BTCPrediction:
    """创建示例BTC预测结果"""
    now = datetime.now(timezone.utc)
    return BTCPrediction(
        direction="bullish",
        confidence=0.78,
        timestamp=now,
        valid_until=now + timedelta(hours=1),
        metadata={
            "model_version": "1.0.0",
            "input_features": ["ohlcv", "volume", "rsi", "macd"],
            "prediction_horizon": 3600,
            "training_accuracy": 0.85
        }
    )


def demo_basic_operations(cache):
    """演示基础缓存操作"""
    print("\n" + "="*50)
    print("演示基础缓存操作")
    print("="*50)
    
    # 设置和获取简单数据
    test_data = {
        "message": "Hello Redis Cache!",
        "timestamp": datetime.now().isoformat(),
        "numbers": [1, 2, 3, 4, 5],
        "nested": {"key": "value", "count": 42}
    }
    
    print(f"设置缓存数据: {test_data}")
    success = cache.set("demo_data", test_data, ttl=300)
    print(f"设置结果: {'成功' if success else '失败'}")
    
    # 获取数据
    retrieved_data = cache.get("demo_data")
    print(f"获取缓存数据: {retrieved_data}")
    
    # 检查键存在性
    exists = cache.exists("demo_data")
    print(f"键存在性检查: {'存在' if exists else '不存在'}")
    
    # 删除数据
    deleted = cache.delete("demo_data")
    print(f"删除结果: {'成功' if deleted else '失败'}")
    
    # 再次检查
    exists_after_delete = cache.exists("demo_data")
    print(f"删除后键存在性: {'存在' if exists_after_delete else '不存在'}")


def demo_btc_prediction(cache):
    """演示BTC预测结果存储"""
    print("\n" + "="*50)
    print("演示BTC预测结果存储")
    print("="*50)
    
    # 创建BTC预测结果
    prediction = create_sample_btc_prediction()
    print(f"创建BTC预测: 方向={prediction.direction}, 置信度={prediction.confidence}")
    print(f"预测时间: {prediction.timestamp}")
    print(f"有效期至: {prediction.valid_until}")
    print(f"元数据: {prediction.metadata}")
    
    # 存储预测结果
    success = cache.set_btc_prediction(prediction)
    print(f"存储结果: {'成功' if success else '失败'}")
    
    # 获取预测结果
    retrieved_prediction = cache.get_btc_prediction()
    if retrieved_prediction:
        print(f"获取预测: 方向={retrieved_prediction.direction}, 置信度={retrieved_prediction.confidence}")
        print(f"数据完整性: {'一致' if retrieved_prediction.metadata == prediction.metadata else '不一致'}")
    else:
        print("获取预测失败")


def demo_nautilus_bars(cache):
    """演示Nautilus Bar对象序列化"""
    print("\n" + "="*50)
    print("演示Nautilus Bar对象序列化")
    print("="*50)
    
    # 创建示例Bar对象
    bars = create_sample_bars()
    print(f"创建了{len(bars)}个Nautilus Bar对象")
    
    # 显示第一个Bar的详细信息
    first_bar = bars[0]
    print(f"第一个Bar详情:")
    print(f"  交易对: {first_bar.bar_type.instrument_id}")
    print(f"  开盘价: {first_bar.open}")
    print(f"  最高价: {first_bar.high}")
    print(f"  最低价: {first_bar.low}")
    print(f"  收盘价: {first_bar.close}")
    print(f"  成交量: {first_bar.volume}")
    
    # 存储Bar对象
    success = cache.set_nautilus_bars("demo_bars", bars, ttl=600)
    print(f"存储{len(bars)}个Bar对象: {'成功' if success else '失败'}")
    
    # 获取Bar对象
    retrieved_bars = cache.get_nautilus_bars("demo_bars")
    if retrieved_bars:
        print(f"获取到{len(retrieved_bars)}个Bar对象")
        
        # 验证数据完整性
        all_match = True
        for i, (original, retrieved) in enumerate(zip(bars, retrieved_bars)):
            if (original.open != retrieved.open or 
                original.high != retrieved.high or 
                original.low != retrieved.low or 
                original.close != retrieved.close or 
                original.volume != retrieved.volume):
                all_match = False
                break
        
        print(f"数据完整性验证: {'通过' if all_match else '失败'}")
    else:
        print("获取Bar对象失败")
    
    # 清理
    cache.delete("nautilus_bars:demo_bars")


def demo_model_status(cache):
    """演示模型状态管理"""
    print("\n" + "="*50)
    print("演示模型状态管理")
    print("="*50)
    
    # 设置BTC预测模型状态
    btc_metadata = {
        "version": "1.0.0",
        "loaded_at": datetime.now().isoformat(),
        "model_size_mb": 15.2,
        "accuracy": 0.85,
        "last_training": "2024-01-01"
    }
    
    success = cache.set_model_status("btc_predictor", "available", btc_metadata)
    print(f"设置BTC预测模型状态: {'成功' if success else '失败'}")
    
    # 设置交易AI模型状态
    trading_metadata = {
        "version": "2.1.0",
        "loaded_at": datetime.now().isoformat(),
        "model_size_mb": 8.7,
        "inference_latency_ms": 45,
        "last_update": "2024-01-15"
    }
    
    success = cache.set_model_status("trading_ai", "available", trading_metadata)
    print(f"设置交易AI模型状态: {'成功' if success else '失败'}")
    
    # 获取模型状态
    btc_status = cache.get_model_status("btc_predictor")
    if btc_status:
        print(f"BTC预测模型状态: {btc_status['status']}")
        print(f"BTC模型版本: {btc_status['metadata']['version']}")
        print(f"BTC模型准确率: {btc_status['metadata']['accuracy']}")
    
    trading_status = cache.get_model_status("trading_ai")
    if trading_status:
        print(f"交易AI模型状态: {trading_status['status']}")
        print(f"交易AI版本: {trading_status['metadata']['version']}")
        print(f"交易AI延迟: {trading_status['metadata']['inference_latency_ms']}ms")
    
    # 模拟模型故障
    error_metadata = {
        "error": "模型文件损坏",
        "error_time": datetime.now().isoformat(),
        "retry_count": 3
    }
    
    cache.set_model_status("btc_predictor", "unavailable", error_metadata)
    print("模拟BTC预测模型故障...")
    
    updated_status = cache.get_model_status("btc_predictor")
    if updated_status:
        print(f"更新后BTC模型状态: {updated_status['status']}")
        print(f"错误信息: {updated_status['metadata']['error']}")


def demo_health_check(cache):
    """演示健康检查"""
    print("\n" + "="*50)
    print("演示健康检查")
    print("="*50)
    
    health_info = cache.health_check()
    
    print(f"服务名称: {health_info['service']}")
    print(f"检查时间: {health_info['timestamp']}")
    print(f"服务状态: {health_info['status']}")
    print(f"连接状态: {'已连接' if health_info['connected'] else '未连接'}")
    print(f"响应延迟: {health_info['latency_ms']}ms")
    print(f"内存使用: {health_info.get('memory_usage', '未知')}")
    
    if health_info.get('error'):
        print(f"错误信息: {health_info['error']}")


def main():
    """主函数"""
    print("Redis缓存系统演示")
    print("="*50)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 加载配置
        config = load_config()
        logger.info("配置文件加载成功")
        
        # 初始化缓存系统
        if not initialize_cache(config):
            print("❌ 缓存系统初始化失败")
            return
        
        print("✅ 缓存系统初始化成功")
        
        # 获取缓存实例
        cache = get_cache()
        if not cache:
            print("❌ 无法获取缓存实例")
            return
        
        print("✅ 缓存实例获取成功")
        
        # 运行各种演示
        demo_basic_operations(cache)
        demo_btc_prediction(cache)
        demo_nautilus_bars(cache)
        demo_model_status(cache)
        demo_health_check(cache)
        
        print("\n" + "="*50)
        print("演示完成！")
        print("="*50)
        
    except Exception as e:
        logger.error(f"演示过程中发生异常: {e}")
        print(f"❌ 演示失败: {e}")


if __name__ == "__main__":
    main()