#!/usr/bin/env python3
"""
配置管理系统使用示例

演示如何在AI交易系统中使用配置管理功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_trading.core.config import Config<PERSON>anager, ConfigHelper, get_config_manager, get_config
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def basic_usage_demo():
    """基础使用示例"""
    print("=" * 60)
    print("基础配置管理使用示例")
    print("=" * 60)
    
    # 1. 获取全局配置管理器
    config_manager = get_config_manager()
    
    # 2. 加载配置
    config = config_manager.load_config()
    
    # 3. 访问配置信息
    print(f"交易员ID: {config.system.trader_id}")
    print(f"系统时区: {config.system.timezone}")
    print(f"日志级别: {config.system.log_level}")
    print(f"Redis主机: {config.redis.host}:{config.redis.port}")
    print(f"最大杠杆: {config.risk_management.max_leverage}x")
    
    # 4. 使用配置辅助工具
    helper = ConfigHelper(config)
    print(f"Redis连接URL: {helper.get_redis_url()}")
    print(f"SQLite数据库路径: {helper.get_sqlite_path()}")
    
    # 5. 检查策略启用状态
    trend_enabled = helper.is_strategy_enabled("trend_following")
    grid_enabled = helper.is_strategy_enabled("grid_trading")
    print(f"趋势跟踪策略: {'启用' if trend_enabled else '禁用'}")
    print(f"网格交易策略: {'启用' if grid_enabled else '禁用'}")


def model_config_demo():
    """AI模型配置使用示例"""
    print("\n" + "=" * 60)
    print("AI模型配置使用示例")
    print("=" * 60)
    
    config = get_config()
    helper = ConfigHelper(config)
    
    # 遍历所有AI模型配置
    for model_name, model_config in config.ai_models.items():
        print(f"\n模型: {model_name}")
        print(f"  路径: {model_config.model_path}")
        print(f"  版本: {model_config.version}")
        print(f"  推理超时: {model_config.inference_timeout}ms")
        print(f"  更新间隔: {model_config.update_interval}秒")
        
        # 使用辅助工具获取模型路径
        try:
            model_path = helper.get_model_path(model_name)
            print(f"  完整路径: {model_path}")
            print(f"  文件存在: {model_path.exists()}")
        except KeyError as e:
            print(f"  错误: {e}")


def risk_management_demo():
    """风险管理配置使用示例"""
    print("\n" + "=" * 60)
    print("风险管理配置使用示例")
    print("=" * 60)
    
    config = get_config()
    helper = ConfigHelper(config)
    
    # 显示风险管理参数
    risk_config = config.risk_management
    print(f"最大单笔风险: {risk_config.max_trade_risk * 100}%")
    print(f"敞口限制: {risk_config.exposure_limit * 100}%")
    print(f"最大杠杆: {risk_config.max_leverage}x")
    print(f"波动率阈值: {risk_config.volatility_threshold * 100}%")
    
    # 测试熔断级别计算
    print("\n熔断级别测试:")
    test_volatilities = [0.02, 0.04, 0.06, 0.08]
    level_names = ["正常交易", "降低杠杆", "禁用AI", "仅允许平仓"]
    
    for volatility in test_volatilities:
        level = helper.get_circuit_breaker_level(volatility)
        print(f"  波动率 {volatility*100:4.1f}% -> 级别 {level} ({level_names[level]})")
    
    # 测试杠杆调整判断
    print("\n杠杆调整判断测试:")
    test_cases = [(5.0, 5.3), (5.0, 6.0), (10.0, 8.5)]
    
    for current, new in test_cases:
        should_adjust = helper.should_adjust_leverage(current, new)
        diff = abs(new - current)
        print(f"  {current}x -> {new}x (差异{diff:.1f}) -> {'需要调整' if should_adjust else '无需调整'}")


def monitoring_config_demo():
    """监控配置使用示例"""
    print("\n" + "=" * 60)
    print("监控配置使用示例")
    print("=" * 60)
    
    config = get_config()
    helper = ConfigHelper(config)
    
    # 显示延迟阈值
    print("延迟阈值配置:")
    mainstream_threshold = helper.get_latency_threshold("mainstream_coins")
    other_threshold = helper.get_latency_threshold("other_coins")
    ai_threshold = helper.get_latency_threshold("ai_inference")
    
    print(f"  主流币种: {mainstream_threshold}ms")
    print(f"  其他币种: {other_threshold}ms")
    print(f"  AI推理: {ai_threshold}ms")
    
    # 显示性能指标
    print("\n性能监控指标:")
    for metric in config.monitoring.performance_metrics:
        print(f"  - {metric}")


def hot_reload_demo():
    """热重载功能示例"""
    print("\n" + "=" * 60)
    print("热重载功能示例")
    print("=" * 60)
    
    # 创建配置管理器并启用热重载
    config_manager = get_config_manager()
    
    # 添加重载回调
    def on_config_reload(new_config):
        print(f"🔄 配置已重新加载!")
        print(f"   新的交易员ID: {new_config.system.trader_id}")
        print(f"   新的日志级别: {new_config.system.log_level}")
    
    config_manager.add_reload_callback(on_config_reload)
    
    print("热重载监听已启动")
    print("提示: 修改 config/ai_trading_config.yaml 文件来测试热重载功能")
    print("监听时间: 10秒")
    
    # 启动热重载监听
    config_manager.start_hot_reload()
    
    try:
        # 等待10秒，期间可以修改配置文件
        for i in range(10):
            time.sleep(1)
            if i % 2 == 0:
                print(f"  监听中... {i+1}/10秒")
    finally:
        # 停止热重载监听
        config_manager.stop_hot_reload()
        config_manager.remove_reload_callback(on_config_reload)
        print("热重载监听已停止")


def environment_override_demo():
    """环境变量覆盖示例"""
    print("\n" + "=" * 60)
    print("环境变量覆盖示例")
    print("=" * 60)
    
    import os
    
    # 显示当前配置
    config = get_config()
    print("当前配置:")
    print(f"  Redis主机: {config.redis.host}")
    print(f"  Redis端口: {config.redis.port}")
    print(f"  日志级别: {config.system.log_level}")
    
    print("\n支持的环境变量:")
    env_vars = [
        "AI_TRADING_REDIS_HOST",
        "AI_TRADING_REDIS_PORT", 
        "AI_TRADING_REDIS_PASSWORD",
        "AI_TRADING_LOG_LEVEL",
        "AI_TRADING_TRADER_ID",
        "AI_TRADING_MAX_LEVERAGE",
        "BINANCE_API_KEY",
        "BINANCE_API_SECRET"
    ]
    
    for var in env_vars:
        value = os.getenv(var, "未设置")
        print(f"  {var}: {value}")
    
    print("\n提示: 设置环境变量后重新启动程序以查看覆盖效果")


def config_validation_demo():
    """配置验证示例"""
    print("\n" + "=" * 60)
    print("配置验证示例")
    print("=" * 60)
    
    config_manager = get_config_manager()
    
    # 测试有效配置
    print("✅ 当前配置验证通过")
    
    # 演示配置验证规则
    print("\n配置验证规则:")
    print("  - 时区必须是有效的pytz时区")
    print("  - 日志级别必须是: DEBUG, INFO, WARNING, ERROR, CRITICAL")
    print("  - 交易员ID不能为空")
    print("  - max_trade_risk必须在(0,1]范围内")
    print("  - exposure_limit必须在(0,1]范围内")
    print("  - max_leverage必须大于0")
    print("  - Redis端口必须在1-65535范围内")
    print("  - 各种超时和间隔参数必须大于0")
    
    # 演示无效配置示例
    print("\n无效配置示例:")
    invalid_examples = [
        "时区设为 'Invalid/Timezone'",
        "日志级别设为 'INVALID'",
        "max_trade_risk设为 1.5",
        "Redis端口设为 70000",
        "max_leverage设为 -1"
    ]
    
    for example in invalid_examples:
        print(f"  ❌ {example}")


def main():
    """主函数"""
    print("AI交易系统配置管理使用示例")
    print("=" * 80)
    
    try:
        # 运行各种示例
        basic_usage_demo()
        model_config_demo()
        risk_management_demo()
        monitoring_config_demo()
        environment_override_demo()
        config_validation_demo()
        
        # 询问是否运行热重载示例
        print("\n" + "=" * 60)
        response = input("是否运行热重载示例? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            hot_reload_demo()
        
        print("\n" + "=" * 80)
        print("✅ 所有示例运行完成!")
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()