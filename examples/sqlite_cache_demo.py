#!/usr/bin/env python3
"""
SQLite缓存功能演示

演示SQLite本地缓存的完整功能，包括：
1. 基本缓存操作（设置、获取、删除）
2. Nautilus Bar对象存储
3. BTC预测结果管理
4. 模型状态跟踪
5. 与Redis的数据同步
6. 故障切换机制
7. 健康检查和监控
"""

import sys
import yaml
import logging
import time
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ai_trading.data import SQLiteCache, RedisCache, CacheFactory, BTCPrediction
from nautilus_trader.model.data import Bar, BarType, BarSpecification
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.enums import BarAggregation, PriceType
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.core.datetime import dt_to_unix_nanos


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('sqlite_cache_demo.log')
        ]
    )


def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = project_root / "config" / "ai_trading_config.yaml"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"✓ 配置文件加载成功: {config_path}")
        return config
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        # 返回默认配置
        return {
            "sqlite": {
                "db_path": "data/demo_cache.db",
                "timeout": 30.0,
                "check_same_thread": False
            },
            "redis": {
                "host": "***************",
                "port": 6379,
                "db": 2,
                "password": "Test@2023",
                "connection_timeout": 5
            }
        }


def create_sample_bars() -> list:
    """创建示例Nautilus Bar对象"""
    print("\\n=== 创建示例Nautilus Bar对象 ===")
    
    bars = []
    base_time = dt_to_unix_nanos(datetime.now(timezone.utc))
    
    # 创建BarType
    instrument_id = InstrumentId.from_str("BTC/USDT.BINANCE")
    bar_spec = BarSpecification(
        step=1,
        aggregation=BarAggregation.MINUTE,
        price_type=PriceType.LAST
    )
    bar_type = BarType(
        instrument_id=instrument_id,
        bar_spec=bar_spec
    )
    
    for i in range(10):
        bar = Bar(
            bar_type=bar_type,
            open=Price.from_str(f"{29000 + i * 50}.00"),
            high=Price.from_str(f"{29100 + i * 50}.00"),
            low=Price.from_str(f"{28900 + i * 50}.00"),
            close=Price.from_str(f"{29050 + i * 50}.00"),
            volume=Quantity.from_str(f"{1000 + i * 100}.0"),
            ts_event=base_time + i * 60_000_000_000,  # 每分钟一个Bar
            ts_init=base_time + i * 60_000_000_000
        )
        bars.append(bar)
    
    print(f"✓ 创建了{len(bars)}个示例Bar对象")
    return bars


def create_sample_btc_prediction() -> BTCPrediction:
    """创建示例BTC预测结果"""
    print("\\n=== 创建示例BTC预测结果 ===")
    
    prediction = BTCPrediction(
        direction="bullish",
        confidence=0.78,
        timestamp=datetime.now(timezone.utc),
        valid_until=datetime.now(timezone.utc) + timedelta(hours=1),
        metadata={
            "model_version": "1.0.0",
            "input_features": ["ohlcv", "volume", "rsi", "macd"],
            "prediction_time_ms": 42,
            "market_conditions": "normal_volatility"
        }
    )
    
    print(f"✓ 创建BTC预测: {prediction.direction}, 置信度: {prediction.confidence}")
    return prediction


def demo_basic_operations(cache: SQLiteCache):
    """演示基本缓存操作"""
    print("\\n=== 演示SQLite缓存基本操作 ===")
    
    # 测试基本的键值对存储
    test_data = {
        "demo_type": "basic_operations",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "price": 29500.50,
            "volume": 1250.75,
            "indicators": {
                "rsi": 65.2,
                "macd": 0.15,
                "bb_upper": 29800.0,
                "bb_lower": 29200.0
            }
        }
    }
    
    # 设置缓存
    key = "demo:basic_data"
    success = cache.set(key, test_data)
    print(f"设置缓存 '{key}': {'成功' if success else '失败'}")
    
    # 获取缓存
    retrieved_data = cache.get(key)
    if retrieved_data:
        print(f"获取缓存成功: {retrieved_data['demo_type']}")
        print(f"  价格: {retrieved_data['data']['price']}")
        print(f"  RSI: {retrieved_data['data']['indicators']['rsi']}")
    else:
        print("获取缓存失败")
    
    # 测试TTL功能
    ttl_key = "demo:ttl_test"
    ttl_data = {"message": "这条数据将在3秒后过期"}
    
    success = cache.set(ttl_key, ttl_data, ttl=3)
    print(f"\\n设置TTL缓存 '{ttl_key}': {'成功' if success else '失败'}")
    
    # 立即检查
    immediate_data = cache.get(ttl_key)
    print(f"立即获取: {'成功' if immediate_data else '失败'}")
    
    # 等待过期
    print("等待3秒让缓存过期...")
    time.sleep(4)
    
    expired_data = cache.get(ttl_key)
    print(f"过期后获取: {'成功' if expired_data else '失败（符合预期）'}")
    
    # 清理测试数据
    cache.delete(key)
    print(f"\\n清理测试数据: {'成功' if not cache.exists(key) else '失败'}")


def demo_nautilus_bars(cache: SQLiteCache, bars: list):
    """演示Nautilus Bar对象存储"""
    print("\\n=== 演示Nautilus Bar对象存储 ===")
    
    key = "BTC_USDT_1m_demo"
    
    # 存储Bar对象
    success = cache.set_nautilus_bars(key, bars)
    print(f"存储{len(bars)}个Bar对象: {'成功' if success else '失败'}")
    
    # 获取Bar对象
    retrieved_bars = cache.get_nautilus_bars(key)
    if retrieved_bars:
        print(f"获取Bar对象成功: {len(retrieved_bars)}个")
        
        # 验证数据完整性
        print("\\n验证Bar对象数据:")
        for i, (original, retrieved) in enumerate(zip(bars[:3], retrieved_bars[:3])):  # 只验证前3个
            print(f"  Bar {i+1}:")
            print(f"    开盘价: {original.open} -> {retrieved.open} {'✓' if original.open == retrieved.open else '✗'}")
            print(f"    收盘价: {original.close} -> {retrieved.close} {'✓' if original.close == retrieved.close else '✗'}")
            print(f"    成交量: {original.volume} -> {retrieved.volume} {'✓' if original.volume == retrieved.volume else '✗'}")
    else:
        print("获取Bar对象失败")
    
    # 测试带TTL的Bar存储
    ttl_key = "BTC_USDT_1m_ttl"
    success = cache.set_nautilus_bars(ttl_key, bars[:3], ttl=5)
    print(f"\\n存储带TTL的Bar对象: {'成功' if success else '失败'}")


def demo_btc_prediction(cache: SQLiteCache, prediction: BTCPrediction):
    """演示BTC预测结果管理"""
    print("\\n=== 演示BTC预测结果管理 ===")
    
    # 存储BTC预测
    success = cache.set_btc_prediction(prediction)
    print(f"存储BTC预测结果: {'成功' if success else '失败'}")
    
    # 获取BTC预测
    retrieved_prediction = cache.get_btc_prediction()
    if retrieved_prediction:
        print("获取BTC预测结果成功:")
        print(f"  方向: {retrieved_prediction.direction}")
        print(f"  置信度: {retrieved_prediction.confidence}")
        print(f"  预测时间: {retrieved_prediction.timestamp}")
        print(f"  有效期至: {retrieved_prediction.valid_until}")
        print(f"  模型版本: {retrieved_prediction.metadata.get('model_version')}")
        print(f"  推理耗时: {retrieved_prediction.metadata.get('prediction_time_ms')}ms")
    else:
        print("获取BTC预测结果失败")


def demo_model_status(cache: SQLiteCache):
    """演示模型状态管理"""
    print("\\n=== 演示模型状态管理 ===")
    
    models = [
        {
            "name": "btc_predictor",
            "status": "available",
            "metadata": {
                "version": "1.0.0",
                "loaded_at": datetime.now().isoformat(),
                "performance": {
                    "accuracy": 0.85,
                    "precision": 0.82,
                    "recall": 0.88,
                    "f1_score": 0.85
                },
                "latency_ms": 45,
                "memory_usage_mb": 128
            }
        },
        {
            "name": "trading_ai",
            "status": "loading",
            "metadata": {
                "version": "2.1.0",
                "loading_progress": 0.75,
                "estimated_completion": "30s",
                "model_size_mb": 256
            }
        }
    ]
    
    # 设置模型状态
    for model in models:
        success = cache.set_model_status(model["name"], model["status"], model["metadata"])
        print(f"设置模型状态 '{model['name']}': {'成功' if success else '失败'}")
    
    print()
    
    # 获取模型状态
    for model in models:
        status_info = cache.get_model_status(model["name"])
        if status_info:
            print(f"模型 '{model['name']}' 状态:")
            print(f"  状态: {status_info['status']}")
            print(f"  版本: {status_info['metadata'].get('version')}")
            if model["name"] == "btc_predictor":
                perf = status_info['metadata'].get('performance', {})
                print(f"  准确率: {perf.get('accuracy')}")
                print(f"  延迟: {status_info['metadata'].get('latency_ms')}ms")
            elif model["name"] == "trading_ai":
                print(f"  加载进度: {status_info['metadata'].get('loading_progress', 0) * 100}%")
        else:
            print(f"获取模型 '{model['name']}' 状态失败")
        print()


def demo_health_check(cache: SQLiteCache):
    """演示健康检查功能"""
    print("\\n=== 演示健康检查功能 ===")
    
    health_info = cache.health_check()
    
    print("SQLite缓存健康状态:")
    print(f"  服务: {health_info['service']}")
    print(f"  状态: {health_info['status']}")
    print(f"  连接: {'正常' if health_info['connected'] else '异常'}")
    print(f"  延迟: {health_info['latency_ms']}ms")
    print(f"  数据库路径: {health_info['db_path']}")
    print(f"  数据库大小: {health_info['db_size_mb']}MB")
    
    print("\\n表统计信息:")
    for table, count in health_info['table_stats'].items():
        print(f"  {table}: {count}条记录")
    
    if health_info.get('error'):
        print(f"  错误信息: {health_info['error']}")


def demo_redis_sync(sqlite_cache: SQLiteCache, redis_config: Dict[str, Any], prediction: BTCPrediction):
    """演示与Redis的数据同步"""
    print("\\n=== 演示与Redis数据同步 ===")
    
    try:
        # 创建Redis连接
        redis_cache = RedisCache(redis_config)
        if not redis_cache.connect():
            print("⚠️  Redis连接失败，跳过同步演示")
            return
        
        print("✓ Redis连接成功")
        
        # 在Redis中设置测试数据
        redis_cache.set_btc_prediction(prediction)
        redis_cache.set_model_status("btc_predictor", "available", {"sync_test": True})
        print("✓ Redis测试数据设置完成")
        
        # 执行同步
        print("\\n开始数据同步...")
        sync_result = sqlite_cache.sync_from_redis(redis_cache)
        
        print("同步结果:")
        print(f"  BTC预测同步: {'成功' if sync_result['btc_prediction'] else '失败'}")
        print(f"  模型状态同步: {sync_result['model_status']}")
        
        if sync_result['errors']:
            print(f"  同步错误: {sync_result['errors']}")
        
        # 验证同步后的数据
        synced_prediction = sqlite_cache.get_btc_prediction()
        if synced_prediction and synced_prediction.direction == prediction.direction:
            print("✓ BTC预测数据同步验证通过")
        else:
            print("✗ BTC预测数据同步验证失败")
        
        # 清理Redis测试数据
        redis_cache.delete("btc_pred:latest")
        redis_cache.delete("model_status:btc_predictor")
        redis_cache.disconnect()
        print("✓ Redis测试数据清理完成")
        
    except Exception as e:
        print(f"⚠️  Redis同步演示异常: {e}")


def demo_cache_factory(config: Dict[str, Any]):
    """演示缓存工厂的故障切换功能"""
    print("\\n=== 演示缓存工厂故障切换 ===")
    
    # 初始化缓存工厂
    factory = CacheFactory()
    success = factory.initialize(config)
    print(f"缓存工厂初始化: {'成功' if success else '失败'}")
    
    if not success:
        print("缓存工厂初始化失败，跳过演示")
        return
    
    # 获取当前缓存
    cache = factory.get_cache()
    if cache:
        print(f"当前缓存类型: {cache.__class__.__name__}")
        
        # 测试缓存操作
        test_key = "factory_demo"
        test_value = {"message": "缓存工厂测试", "timestamp": datetime.now().isoformat()}
        
        if cache.set(test_key, test_value):
            retrieved = cache.get(test_key)
            if retrieved and retrieved["message"] == test_value["message"]:
                print("✓ 缓存工厂功能测试通过")
            else:
                print("✗ 缓存工厂功能测试失败")
        
        # 健康检查
        health_info = factory.health_check()
        print(f"\\n工厂健康状态: {health_info['cache_factory']['status']}")
        print(f"当前使用缓存: {health_info['cache_factory']['current_cache']}")
        
        # 清理测试数据
        cache.delete(test_key)
    else:
        print("获取缓存实例失败")
    
    # 关闭工厂
    factory.shutdown()
    print("✓ 缓存工厂已关闭")


def demo_backup_functionality(cache: SQLiteCache):
    """演示备份功能"""
    print("\\n=== 演示备份功能 ===")
    
    # 确保有一些数据可以备份
    cache.set("backup_demo", {"data": "备份测试数据", "timestamp": datetime.now().isoformat()})
    
    # 执行备份
    backup_path = "data/demo_backup.db"
    success = cache.backup_to_file(backup_path)
    print(f"数据库备份: {'成功' if success else '失败'}")
    
    if success:
        backup_file = Path(backup_path)
        if backup_file.exists():
            size_mb = backup_file.stat().st_size / (1024 * 1024)
            print(f"备份文件: {backup_path}")
            print(f"备份大小: {size_mb:.2f}MB")
        else:
            print("备份文件不存在")


def main():
    """主函数"""
    print("=== SQLite缓存功能演示 ===")
    print(f"演示时间: {datetime.now()}")
    
    # 设置日志
    setup_logging()
    
    # 加载配置
    config = load_config()
    
    # 确保数据目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    try:
        # 创建SQLite缓存实例
        sqlite_config = config.get("sqlite", {})
        sqlite_cache = SQLiteCache(sqlite_config)
        
        if not sqlite_cache.connect():
            print("✗ SQLite缓存连接失败")
            return
        
        print("✓ SQLite缓存连接成功")
        
        # 创建示例数据
        sample_bars = create_sample_bars()
        sample_prediction = create_sample_btc_prediction()
        
        # 运行各种演示
        demo_basic_operations(sqlite_cache)
        demo_nautilus_bars(sqlite_cache, sample_bars)
        demo_btc_prediction(sqlite_cache, sample_prediction)
        demo_model_status(sqlite_cache)
        demo_health_check(sqlite_cache)
        demo_redis_sync(sqlite_cache, config.get("redis", {}), sample_prediction)
        demo_backup_functionality(sqlite_cache)
        
        # 演示缓存工厂
        demo_cache_factory(config)
        
        # 关闭连接
        sqlite_cache.disconnect()
        print("\\n✓ SQLite缓存连接已关闭")
        
        print("\\n=== 演示完成 ===")
        print("所有SQLite缓存功能演示成功完成！")
        
    except Exception as e:
        print(f"\\n✗ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()