# 配置管理系统文档

## 概述

AI交易系统的配置管理系统提供了完整的配置文件管理功能，包括：

- **YAML配置文件加载**：支持结构化的配置文件格式
- **环境变量覆盖**：支持通过环境变量覆盖配置参数
- **配置验证**：自动验证配置参数的有效性
- **热重载**：支持配置文件变更时自动重新加载
- **线程安全**：支持多线程环境下的并发访问
- **配置备份**：支持配置文件的自动备份

## 配置文件结构

### 主配置文件

配置文件位于 `config/ai_trading_config.yaml`，包含以下主要部分：

```yaml
# 系统配置
system:
  timezone: "Asia/Shanghai"      # 系统时区
  log_level: "INFO"             # 日志级别
  trader_id: "AI-TRADER-001"    # 交易员ID

# AI模型配置
ai_models:
  btc_predictor:
    model_path: "models/btc_predictor/model.onnx"
    version: "1.0.0"
    update_interval: 3600       # 更新间隔（秒）
    inference_timeout: 100      # 推理超时（毫秒）
  
  trading_ai:
    model_path: "models/trading_ai/trading_ai.onnx"
    version: "1.0.0"
    inference_timeout: 100

# Redis缓存配置
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null
  connection_timeout: 5

# SQLite本地缓存配置
sqlite:
  db_path: "data/ai_trading.db"
  backup_interval: 3600

# 风险管理配置
risk_management:
  max_trade_risk: 0.02          # 单笔最大风险2%
  exposure_limit: 0.15          # 单币种敞口限制15%
  max_leverage: 10              # 最大杠杆10倍
  volatility_threshold: 0.1     # 波动率调整阈值10%
  
  # 熔断机制
  circuit_breaker:
    daily_loss_limit: 0.05      # 单日最大亏损5%
    volatility_limits:
      level_1: 0.03             # 3%波动率：降杠杆至5x
      level_2: 0.05             # 5%波动率：禁用AI切换规则引擎
      level_3: 0.07             # 7%波动率：仅允许平仓

# 交易策略配置
strategies:
  trend_following:
    enabled: true
    confidence_threshold: 0.6
    btc_weight: 0.3
  
  grid_trading:
    enabled: true
    base_spacing: 0.005
    max_levels: 10

# 性能监控配置
monitoring:
  latency_thresholds:
    mainstream_coins: 100       # 主流币种延迟阈值(ms)
    other_coins: 300            # 其他币种延迟阈值(ms)
    ai_inference: 50            # AI推理延迟阈值(ms)
  
  performance_metrics:
    - "ai_inference_latency"
    - "order_execution_latency"
    - "trade_success_rate"
    - "daily_pnl"
```

## 基础使用

### 1. 加载配置

```python
from src.ai_trading.core.config import get_config_manager, get_config

# 方法1：使用全局配置管理器
config_manager = get_config_manager()
config = config_manager.load_config()

# 方法2：直接获取配置（推荐）
config = get_config()

# 访问配置信息
print(f"交易员ID: {config.system.trader_id}")
print(f"Redis主机: {config.redis.host}")
print(f"最大杠杆: {config.risk_management.max_leverage}")
```

### 2. 使用配置辅助工具

```python
from src.ai_trading.core.config import ConfigHelper

helper = ConfigHelper()

# 获取时区对象
timezone = helper.get_timezone()

# 获取模型路径
btc_model_path = helper.get_model_path("btc_predictor")

# 获取Redis连接URL
redis_url = helper.get_redis_url()

# 检查策略是否启用
trend_enabled = helper.is_strategy_enabled("trend_following")

# 获取延迟阈值
threshold = helper.get_latency_threshold("mainstream_coins")

# 计算熔断级别
level = helper.get_circuit_breaker_level(0.04)  # 4%波动率

# 判断是否需要调整杠杆
should_adjust = helper.should_adjust_leverage(5.0, 6.0)
```

## 环境变量覆盖

系统支持通过环境变量覆盖配置文件中的参数：

### 支持的环境变量

| 环境变量 | 配置路径 | 说明 |
|---------|---------|------|
| `AI_TRADING_LOG_LEVEL` | `system.log_level` | 日志级别 |
| `AI_TRADING_TRADER_ID` | `system.trader_id` | 交易员ID |
| `AI_TRADING_TIMEZONE` | `system.timezone` | 系统时区 |
| `AI_TRADING_REDIS_HOST` | `redis.host` | Redis主机 |
| `AI_TRADING_REDIS_PORT` | `redis.port` | Redis端口 |
| `AI_TRADING_REDIS_PASSWORD` | `redis.password` | Redis密码 |
| `AI_TRADING_MAX_LEVERAGE` | `risk_management.max_leverage` | 最大杠杆 |
| `BINANCE_API_KEY` | `nautilus.data_clients.binance.api_key` | Binance API密钥 |
| `BINANCE_API_SECRET` | `nautilus.data_clients.binance.api_secret` | Binance API密钥 |

### 使用示例

```bash
# Windows
set AI_TRADING_REDIS_HOST=production-redis
set AI_TRADING_LOG_LEVEL=DEBUG
python main.py

# Linux/Mac
export AI_TRADING_REDIS_HOST=production-redis
export AI_TRADING_LOG_LEVEL=DEBUG
python main.py
```

## 热重载功能

### 启用热重载

```python
from src.ai_trading.core.config import ConfigManager

# 创建配置管理器并启用热重载
config_manager = ConfigManager(enable_hot_reload=True)

# 添加重载回调
def on_config_reload(new_config):
    print(f"配置已重新加载: {new_config.system.trader_id}")

config_manager.add_reload_callback(on_config_reload)

# 使用上下文管理器自动管理热重载
with config_manager:
    config = config_manager.load_config()
    # 在此期间修改配置文件会自动触发重新加载
    # ... 业务逻辑 ...
```

### 手动检查配置变更

```python
# 手动检查配置文件是否有变更
if config_manager.reload_if_changed():
    print("配置已更新")
    new_config = config_manager.get_config()
```

## 配置验证

系统会自动验证配置参数的有效性：

### 验证规则

- **时区验证**：必须是有效的pytz时区
- **日志级别**：必须是 DEBUG, INFO, WARNING, ERROR, CRITICAL 之一
- **交易员ID**：不能为空
- **风险参数**：
  - `max_trade_risk` 必须在 (0,1] 范围内
  - `exposure_limit` 必须在 (0,1] 范围内
  - `max_leverage` 必须大于 0
- **网络参数**：
  - Redis端口必须在 1-65535 范围内
  - 各种超时参数必须大于 0

### 自定义验证

```python
# 验证配置变更
errors = config_manager.validate_config_changes(new_config_dict)
if errors:
    print(f"配置验证失败: {errors}")
else:
    print("配置验证通过")
```

## 配置备份

### 自动备份

```python
# 备份当前配置
backup_path = config_manager.backup_config()
print(f"配置已备份到: {backup_path}")

# 指定备份路径
backup_path = config_manager.backup_config("config/backup/config_v1.yaml")
```

## 命令行工具

系统提供了配置管理命令行工具：

### 验证配置

```bash
python scripts/config_tool.py validate
```

### 显示配置信息

```bash
python scripts/config_tool.py info
```

### 备份配置

```bash
python scripts/config_tool.py backup --output config/backup/config_backup.yaml
```

### 测试热重载

```bash
python scripts/config_tool.py test-reload --duration 30
```

### 导出配置模式

```bash
python scripts/config_tool.py export-schema --output config_schema.json
```

## 最佳实践

### 1. 配置文件组织

- 将敏感信息（如API密钥）通过环境变量设置
- 使用版本控制管理配置文件，但排除敏感信息
- 为不同环境（开发、测试、生产）维护不同的配置文件

### 2. 环境变量使用

```bash
# 创建 .env 文件（不要提交到版本控制）
AI_TRADING_REDIS_PASSWORD=your_redis_password
BINANCE_API_KEY=your_api_key
BINANCE_API_SECRET=your_api_secret
```

### 3. 配置验证

```python
# 在应用启动时验证关键配置
config = get_config()
helper = ConfigHelper(config)

# 验证模型文件存在
for model_name in config.ai_models:
    model_path = helper.get_model_path(model_name)
    if not model_path.exists():
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

# 验证Redis连接
import redis
try:
    redis_client = redis.from_url(helper.get_redis_url())
    redis_client.ping()
except redis.ConnectionError:
    raise ConnectionError("无法连接到Redis服务器")
```

### 4. 热重载使用

```python
# 在长期运行的服务中使用热重载
class TradingService:
    def __init__(self):
        self.config_manager = get_config_manager()
        self.config_manager.add_reload_callback(self.on_config_reload)
        self.config_manager.start_hot_reload()
    
    def on_config_reload(self, new_config):
        # 重新初始化依赖配置的组件
        self.reinitialize_components(new_config)
    
    def shutdown(self):
        self.config_manager.stop_hot_reload()
```

## 故障排除

### 常见问题

1. **配置文件格式错误**
   ```
   yaml.YAMLError: while parsing a block mapping
   ```
   解决：检查YAML文件格式，确保缩进正确

2. **时区验证失败**
   ```
   ValueError: 无效的时区: Invalid/Timezone
   ```
   解决：使用有效的pytz时区名称，如 "Asia/Shanghai"

3. **模型文件不存在**
   ```
   WARNING: AI模型文件不存在: models/btc_predictor/model.onnx
   ```
   解决：确保模型文件路径正确，或在后续阶段添加模型文件

4. **热重载不工作**
   - 确保启用了热重载功能
   - 检查文件系统权限
   - 确认配置文件路径正确

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('src.ai_trading.core.config').setLevel(logging.DEBUG)

# 检查配置加载过程
config_manager = ConfigManager()
config = config_manager.load_config()

# 验证环境变量覆盖
import os
print("环境变量:")
for key, value in os.environ.items():
    if key.startswith('AI_TRADING_'):
        print(f"  {key}: {value}")
```

## API参考

详细的API文档请参考源代码中的docstring注释。主要类和函数：

- `ConfigManager`: 配置管理器主类
- `ConfigHelper`: 配置辅助工具类
- `get_config_manager()`: 获取全局配置管理器
- `get_config()`: 获取当前配置
- 各种配置数据类：`SystemConfig`, `AIModelConfig`, `RedisConfig` 等