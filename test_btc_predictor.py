"""
BTC预测AI模块完整测试

测试任务3.1的所有功能：
1. BTCPredictorModel类的ONNX模型加载和预测
2. BTCPredictorScheduler的每小时调度功能
3. 与Nautilus DataEngine的集成
4. 预测结果缓存到Redis
"""

import asyncio
import logging
import sys
import os
import yaml
import json
from datetime import datetime, timezone
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, '.')

from src.ai_trading.models.btc_predictor import BTCPredictorModel
from src.ai_trading.models.btc_predictor_scheduler import BTCPredictorScheduler
from src.ai_trading.data.cache_factory import initialize_cache, get_cache

async def test_btc_predictor_model():
    """测试BTC预测模型基础功能"""
    print("\n" + "=" * 60)
    print("BTC预测模型基础功能测试")
    print("=" * 60)
    
    try:
        # 加载配置
        config_path = "config/ai_trading_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取BTC预测模型配置
        btc_config = config.get("ai_models", {}).get("btc_predictor", {})
        print(f"📋 BTC预测模型配置: {btc_config}")
        
        # 创建BTC预测模型
        btc_model = BTCPredictorModel(btc_config)
        print("✅ BTC预测模型实例创建成功")
        
        # 加载模型
        print("\n🔧 加载ONNX模型...")
        model_loaded = btc_model.load_model()
        if not model_loaded:
            print("❌ 模型加载失败")
            return False
        
        print("✅ ONNX模型加载成功")
        
        # 获取模型信息
        model_info = btc_model.get_model_info()
        print(f"\n📊 模型信息:")
        print(f"   名称: {model_info.name}")
        print(f"   版本: {model_info.version}")
        print(f"   类型: {model_info.model_type}")
        print(f"   路径: {model_info.model_path}")
        print(f"   输入形状: {model_info.input_shape}")
        print(f"   输出形状: {model_info.output_shape}")
        if model_info.model_size:
            print(f"   文件大小: {model_info.model_size / 1024 / 1024:.2f} MB")
        
        # 准备测试数据
        print("\n🧪 准备测试数据...")
        test_data = create_test_btc_data()
        
        # 验证输入数据
        is_valid = btc_model.validate_input_data(test_data)
        print(f"   数据验证: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        if not is_valid:
            print("❌ 测试数据格式错误")
            return False
        
        # 执行预测
        print("\n🔮 执行BTC预测...")
        prediction_result = btc_model.predict(test_data)
        
        print(f"✅ 预测完成:")
        print(f"   方向: {prediction_result.direction}")
        print(f"   置信度: {prediction_result.confidence:.3f}")
        print(f"   时间戳: {prediction_result.timestamp}")
        print(f"   有效期: {prediction_result.valid_until}")
        
        # 显示概率分布
        if 'probabilities' in prediction_result.metadata:
            probs = prediction_result.metadata['probabilities']
            print(f"   概率分布:")
            print(f"     看跌: {probs['bearish']:.3f}")
            print(f"     震荡: {probs['sideways']:.3f}")
            print(f"     看涨: {probs['bullish']:.3f}")
        
        # 显示推理时间
        if 'inference_time_ms' in prediction_result.metadata:
            inference_time = prediction_result.metadata['inference_time_ms']
            print(f"   推理时间: {inference_time:.2f}ms")
            
            # 检查延迟要求（≤50ms）
            if inference_time <= 50:
                print("   ✅ 推理延迟符合要求（≤50ms）")
            else:
                print("   ⚠️  推理延迟超过要求（>50ms）")
        
        # 测试多次预测的性能
        print("\n⚡ 性能测试（10次预测）...")
        start_time = datetime.now()
        
        for i in range(10):
            result = btc_model.predict(test_data)
        
        total_time = (datetime.now() - start_time).total_seconds() * 1000
        avg_time = total_time / 10
        
        print(f"   总时间: {total_time:.2f}ms")
        print(f"   平均时间: {avg_time:.2f}ms")
        print(f"   性能评估: {'✅ 优秀' if avg_time <= 50 else '⚠️  需要优化' if avg_time <= 100 else '❌ 性能不足'}")
        
        # 获取推理统计
        stats = btc_model.get_inference_stats()
        print(f"\n📈 推理统计:")
        print(f"   总预测次数: {stats['inference_count']}")
        print(f"   平均推理时间: {stats['avg_inference_time_ms']:.2f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ BTC预测模型测试失败: {e}")
        logging.error("BTC预测模型测试异常", exc_info=True)
        return False

async def test_btc_predictor_scheduler():
    """测试BTC预测调度器"""
    print("\n" + "=" * 60)
    print("BTC预测调度器测试")
    print("=" * 60)
    
    try:
        # 加载配置
        config_path = "config/ai_trading_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 初始化缓存系统
        print("🔧 初始化缓存系统...")
        cache_init_success = initialize_cache(config)
        if not cache_init_success:
            print("❌ 缓存系统初始化失败")
            return False
        print("✅ 缓存系统初始化成功")
        
        # 创建BTC预测调度器
        scheduler = BTCPredictorScheduler(config)
        print("✅ BTC预测调度器创建成功")
        
        # 初始化调度器
        print("\n🔧 初始化调度器组件...")
        init_success = await scheduler.initialize()
        if not init_success:
            print("❌ 调度器初始化失败")
            return False
        print("✅ 调度器初始化成功")
        
        # 获取调度器状态
        status = scheduler.get_status()
        print(f"\n📊 调度器状态:")
        print(f"   已初始化: {status['is_initialized']}")
        print(f"   运行中: {status['is_running']}")
        print(f"   模型已加载: {status['model_loaded']}")
        print(f"   数据提供器已初始化: {status['data_provider_initialized']}")
        print(f"   缓存可用: {status['cache_available']}")
        print(f"   时区: {status['timezone']}")
        print(f"   更新间隔: {status['update_interval']}秒")
        
        # 执行立即预测测试
        print("\n🔮 执行立即预测测试...")
        prediction_result = await scheduler.execute_immediate_prediction()
        
        if prediction_result:
            print("✅ 立即预测成功:")
            print(f"   方向: {prediction_result['direction']}")
            print(f"   置信度: {prediction_result['confidence']:.3f}")
            print(f"   时间戳: {prediction_result['timestamp']}")
            
            # 验证缓存存储
            cache_interface = get_cache()
            if cache_interface:
                cached_result = cache_interface.get("btc_pred:latest")
                if cached_result:
                    print("✅ 预测结果已成功缓存到Redis")
                else:
                    print("⚠️  预测结果缓存验证失败")
            
        else:
            print("❌ 立即预测失败")
            return False
        
        # 测试调度器启动和停止
        print("\n⏰ 测试调度器启动...")
        start_success = await scheduler.start()
        if start_success:
            print("✅ 调度器启动成功")
            
            # 等待几秒钟
            print("   等待3秒...")
            await asyncio.sleep(3)
            
            # 停止调度器
            print("   停止调度器...")
            stop_success = await scheduler.stop()
            if stop_success:
                print("✅ 调度器停止成功")
            else:
                print("❌ 调度器停止失败")
        else:
            print("❌ 调度器启动失败")
            return False
        
        # 清理资源
        await scheduler.cleanup()
        print("✅ 调度器资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ BTC预测调度器测试失败: {e}")
        logging.error("BTC预测调度器测试异常", exc_info=True)
        return False

async def test_btc_predictor_integration():
    """测试BTC预测器完整集成"""
    print("\n" + "=" * 60)
    print("BTC预测器完整集成测试")
    print("=" * 60)
    
    try:
        # 加载配置
        config_path = "config/ai_trading_config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 初始化缓存系统
        print("🔧 初始化缓存系统...")
        cache_init_success = initialize_cache(config)
        if not cache_init_success:
            print("❌ 缓存系统初始化失败")
            return False
        
        # 导入BTCPredictor类
        from src.ai_trading.models.btc_predictor import BTCPredictor
        
        # 创建BTC预测器
        btc_predictor = BTCPredictor(config)
        print("✅ BTC预测器创建成功")
        
        # 初始化预测器
        print("\n🔧 初始化BTC预测器...")
        init_success = await btc_predictor.initialize()
        if not init_success:
            print("❌ BTC预测器初始化失败")
            return False
        print("✅ BTC预测器初始化成功")
        
        # 执行手动预测
        print("\n🔮 执行手动预测...")
        manual_result = await btc_predictor.predict_now()
        if manual_result:
            print("✅ 手动预测成功:")
            print(f"   方向: {manual_result['direction']}")
            print(f"   置信度: {manual_result['confidence']:.3f}")
        else:
            print("❌ 手动预测失败")
        
        # 获取预测器状态
        status = btc_predictor.get_status()
        print(f"\n📊 预测器状态:")
        print(f"   已初始化: {status.get('is_initialized', False)}")
        print(f"   运行中: {status.get('is_running', False)}")
        print(f"   总预测次数: {status.get('total_predictions', 0)}")
        print(f"   成功预测次数: {status.get('successful_predictions', 0)}")
        print(f"   失败预测次数: {status.get('failed_predictions', 0)}")
        
        # 清理资源
        await btc_predictor.cleanup()
        print("✅ BTC预测器资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ BTC预测器集成测试失败: {e}")
        logging.error("BTC预测器集成测试异常", exc_info=True)
        return False

def create_test_btc_data():
    """创建测试用的BTC数据"""
    # 创建24个时间步的OHLCV数据
    np.random.seed(42)  # 确保结果可重现
    
    base_price = 114000.0
    ohlcv_data = []
    
    for i in range(24):
        # 模拟价格波动
        price_change = np.random.normal(0, 100)  # 正态分布的价格变化
        current_price = base_price + price_change
        
        # 生成OHLCV数据
        open_price = current_price + np.random.normal(0, 50)
        high_price = max(open_price, current_price) + abs(np.random.normal(0, 30))
        low_price = min(open_price, current_price) - abs(np.random.normal(0, 30))
        close_price = current_price
        volume = abs(np.random.normal(1000, 200))
        
        ohlcv_data.append([open_price, high_price, low_price, close_price, volume])
        base_price = current_price  # 下一个时间步的基准价格
    
    # 创建73个技术指标特征（每个时间步）
    technical_indicators = []
    for i in range(24):
        # 模拟技术指标数据
        indicators = []
        
        # 基础技术指标（18个）
        indicators.extend([
            np.random.uniform(0, 1),    # sma_5 (标准化)
            np.random.uniform(0, 1),    # sma_10
            np.random.uniform(0, 1),    # sma_20
            np.random.uniform(0, 1),    # sma_50
            np.random.uniform(0, 1),    # ema_12
            np.random.uniform(0, 1),    # ema_26
            np.random.uniform(-1, 1),   # macd
            np.random.uniform(-1, 1),   # macd_signal
            np.random.uniform(-1, 1),   # macd_histogram
            np.random.uniform(0, 100),  # rsi
            np.random.uniform(0, 1),    # bb_upper
            np.random.uniform(0, 1),    # bb_lower
            np.random.uniform(0, 1),    # bb_middle
            np.random.uniform(0, 1),    # atr
            np.random.uniform(0, 1),    # volume_sma
            np.random.uniform(0.5, 2),  # volume_ratio
            np.random.uniform(-100, 100), # price_change
            np.random.uniform(-5, 5)    # price_change_pct
        ])
        
        # 填充到73个特征
        while len(indicators) < 73:
            indicators.append(np.random.uniform(-1, 1))
        
        # 确保正好73个特征
        indicators = indicators[:73]
        technical_indicators.append(indicators)
    
    return {
        'ohlcv_data': ohlcv_data,
        'technical_indicators': technical_indicators,
        'timestamp': datetime.now(timezone.utc).isoformat()
    }

async def main():
    """主测试函数"""
    print("=" * 80)
    print("BTC预测AI模块完整测试 - 任务3.1验证")
    print("=" * 80)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: BTC预测模型基础功能
        if await test_btc_predictor_model():
            success_count += 1
        
        # 等待一下，确保资源清理
        await asyncio.sleep(0.1)
        
        # 测试2: BTC预测调度器
        if await test_btc_predictor_scheduler():
            success_count += 1
        
        # 等待一下，确保资源清理
        await asyncio.sleep(0.1)
        
        # 测试3: BTC预测器完整集成
        if await test_btc_predictor_integration():
            success_count += 1
        
        # 最终等待，确保所有异步任务完成
        await asyncio.sleep(0.5)
        
        print("\n" + "=" * 80)
        print("测试结果汇总")
        print("=" * 80)
        
        print(f"✅ 通过测试: {success_count}/{total_tests}")
        
        if success_count == total_tests:
            print("\n🎉 任务3.1 - BTC预测AI模块实现完成！")
            print("\n📋 已实现功能:")
            print("✅ BTCPredictorModel类 - ONNX模型加载和预测")
            print("✅ 输入数据验证和预处理")
            print("✅ 预测结果格式化和元数据")
            print("✅ 性能统计和推理时间监控")
            print("✅ BTCPredictorScheduler类 - 每小时调度")
            print("✅ BTCPredictor高级接口 - 完整集成")
            print("✅ 与Nautilus DataEngine集成")
            print("✅ Redis缓存存储预测结果")
            print("✅ Asia/Shanghai时区支持")
            print("✅ 错误处理和资源清理")
            
            return True
        else:
            print(f"\n❌ 部分测试失败，需要修复问题")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生严重错误: {e}")
        logging.error("测试异常", exc_info=True)
        return False

async def run_tests_with_cleanup():
    """运行测试并确保完全清理"""
    try:
        success = await main()
        
        # 等待所有异步任务完成
        await asyncio.sleep(1)
        
        # 取消所有待处理的任务
        tasks = [task for task in asyncio.all_tasks() if not task.done()]
        if tasks:
            print(f"\n🔧 清理 {len(tasks)} 个待处理任务...")
            for task in tasks:
                task.cancel()
            
            # 等待任务取消完成
            await asyncio.gather(*tasks, return_exceptions=True)
        
        return success
        
    except Exception as e:
        print(f"\n💥 测试运行异常: {e}")
        return False

if __name__ == "__main__":
    try:
        # 使用Windows事件循环策略
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 运行测试
            success = loop.run_until_complete(run_tests_with_cleanup())
            
            if success:
                print("\n🎉 所有测试通过，任务3.1实现完成！")
                exit_code = 0
            else:
                print("\n❌ 测试失败，需要修复问题")
                exit_code = 1
                
        finally:
            # 确保事件循环完全清理
            try:
                # 取消所有待处理的任务
                pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
                if pending_tasks:
                    print(f"\n🔧 最终清理 {len(pending_tasks)} 个待处理任务...")
                    for task in pending_tasks:
                        task.cancel()
                    
                    # 运行一次以处理取消的任务
                    loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                
                # 关闭事件循环
                loop.close()
                print("✅ 事件循环已完全关闭")
                
            except Exception as cleanup_error:
                print(f"⚠️  事件循环清理警告: {cleanup_error}")
        
        sys.exit(exit_code)
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 严重错误: {e}")
        sys.exit(1)