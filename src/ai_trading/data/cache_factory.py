"""
缓存工厂类

该模块提供缓存实例的统一创建和管理，支持Redis和SQLite缓存的自动切换。
实现单例模式确保全局缓存实例的一致性。
"""

import logging
from typing import Optional, Dict, Any
from threading import Lock
from datetime import datetime

from .cache_interface import CacheInterface
from .redis_cache import RedisCache
from .sqlite_cache import SQLiteCache


class CacheFactory:
    """缓存工厂类
    
    使用单例模式管理缓存实例，支持Redis主缓存和SQLite备用缓存的自动切换。
    """
    
    _instance: Optional['CacheFactory'] = None
    _lock = Lock()
    
    def __new__(cls) -> 'CacheFactory':
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化缓存工厂"""
        if hasattr(self, '_initialized'):
            return
        
        self.logger = logging.getLogger(__name__)
        self._primary_cache: Optional[CacheInterface] = None
        self._backup_cache: Optional[CacheInterface] = None
        self._current_cache: Optional[CacheInterface] = None
        self._config: Optional[Dict[str, Any]] = None
        self._initialized = True
        
        self.logger.info("缓存工厂初始化完成")
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化缓存系统
        
        Args:
            config: 完整的系统配置字典
            
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        try:
            self._config = config
            
            # 初始化Redis主缓存
            redis_config = config.get("redis", {})
            if redis_config:
                self._primary_cache = RedisCache(redis_config)
                if self._primary_cache.connect():
                    self._current_cache = self._primary_cache
                    self.logger.info("Redis主缓存初始化成功")
                else:
                    self.logger.error("Redis主缓存连接失败")
            
            # 初始化SQLite备用缓存
            sqlite_config = config.get("sqlite", {})
            if sqlite_config:
                self._backup_cache = SQLiteCache(sqlite_config)
                if self._backup_cache.connect():
                    self.logger.info("SQLite备用缓存初始化成功")
                    # 如果Redis不可用，使用SQLite作为主缓存
                    if self._current_cache is None:
                        self._current_cache = self._backup_cache
                        self.logger.warning("Redis不可用，使用SQLite作为主缓存")
                else:
                    self.logger.error("SQLite备用缓存连接失败")
            
            if self._current_cache is None:
                self.logger.error("所有缓存系统初始化失败")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"缓存系统初始化异常: {e}")
            return False
    
    def get_cache(self) -> Optional[CacheInterface]:
        """获取当前可用的缓存实例
        
        自动检查主缓存状态，必要时切换到备用缓存。
        支持故障切换和自动恢复。
        
        Returns:
            Optional[CacheInterface]: 可用的缓存实例，无可用缓存返回None
        """
        try:
            # 检查当前缓存是否可用
            if self._current_cache and self._current_cache.is_connected():
                return self._current_cache
            
            # 尝试重连主缓存（Redis）
            if self._primary_cache and self._primary_cache.connect():
                # 如果之前使用的是备用缓存，现在主缓存恢复了，需要同步数据
                if self._current_cache == self._backup_cache and self._backup_cache:
                    self.logger.info("主缓存恢复，开始数据同步")
                    self._sync_backup_to_primary()
                
                self._current_cache = self._primary_cache
                self.logger.info("Redis主缓存重连成功")
                return self._current_cache
            
            # 切换到备用缓存（SQLite）
            if self._backup_cache:
                if not self._backup_cache.is_connected():
                    if not self._backup_cache.connect():
                        self.logger.error("SQLite备用缓存连接失败")
                        return None
                
                # 如果之前使用的是主缓存，现在切换到备用缓存，需要同步数据
                if self._current_cache == self._primary_cache:
                    self.logger.warning("主缓存不可用，切换到SQLite备用缓存")
                    self._sync_primary_to_backup()
                
                self._current_cache = self._backup_cache
                return self._current_cache
            
            self.logger.error("所有缓存系统均不可用")
            return None
            
        except Exception as e:
            self.logger.error(f"获取缓存实例时发生异常: {e}")
            return None
    
    def get_primary_cache(self) -> Optional[CacheInterface]:
        """获取主缓存实例（Redis）
        
        Returns:
            Optional[CacheInterface]: Redis缓存实例，不可用返回None
        """
        return self._primary_cache
    
    def get_backup_cache(self) -> Optional[CacheInterface]:
        """获取备用缓存实例（SQLite）
        
        Returns:
            Optional[CacheInterface]: SQLite缓存实例，不可用返回None
        """
        return self._backup_cache
    
    def health_check(self) -> Dict[str, Any]:
        """缓存系统整体健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        health_info = {
            "cache_factory": {
                "status": "healthy",
                "current_cache": None,
                "primary_cache": None,
                "backup_cache": None
            }
        }
        
        try:
            # 检查主缓存
            if self._primary_cache:
                primary_health = self._primary_cache.health_check()
                health_info["cache_factory"]["primary_cache"] = primary_health
            
            # 检查备用缓存
            if self._backup_cache:
                backup_health = self._backup_cache.health_check()
                health_info["cache_factory"]["backup_cache"] = backup_health
            
            # 确定当前使用的缓存
            current_cache = self.get_cache()
            if current_cache == self._primary_cache:
                health_info["cache_factory"]["current_cache"] = "redis"
            elif current_cache == self._backup_cache:
                health_info["cache_factory"]["current_cache"] = "sqlite"
            else:
                health_info["cache_factory"]["current_cache"] = "none"
                health_info["cache_factory"]["status"] = "error"
            
        except Exception as e:
            health_info["cache_factory"]["status"] = "error"
            health_info["cache_factory"]["error"] = str(e)
        
        return health_info
    
    def _sync_primary_to_backup(self) -> None:
        """从主缓存（Redis）同步数据到备用缓存（SQLite）"""
        try:
            if not self._primary_cache or not self._backup_cache:
                return
            
            # 检查SQLite缓存是否有sync_from_redis方法
            if hasattr(self._backup_cache, 'sync_from_redis'):
                sync_result = self._backup_cache.sync_from_redis(self._primary_cache)
                if sync_result.get("errors"):
                    self.logger.warning(f"数据同步存在错误: {sync_result['errors']}")
                else:
                    self.logger.info("主缓存到备用缓存数据同步完成")
            else:
                self.logger.warning("备用缓存不支持数据同步")
                
        except Exception as e:
            self.logger.error(f"主缓存到备用缓存同步异常: {e}")
    
    def _sync_backup_to_primary(self) -> None:
        """从备用缓存（SQLite）同步数据到主缓存（Redis）
        
        当主缓存恢复时，将备用缓存中的新数据同步回主缓存。
        """
        try:
            if not self._primary_cache or not self._backup_cache:
                return
            
            # 获取SQLite中的BTC预测结果
            btc_prediction = self._backup_cache.get_btc_prediction()
            if btc_prediction:
                if self._primary_cache.set_btc_prediction(btc_prediction):
                    self.logger.info("BTC预测结果已同步到主缓存")
                else:
                    self.logger.error("BTC预测结果同步到主缓存失败")
            
            # 同步模型状态
            model_names = ["btc_predictor", "trading_ai"]
            for model_name in model_names:
                model_status = self._backup_cache.get_model_status(model_name)
                if model_status:
                    if self._primary_cache.set_model_status(
                        model_name, 
                        model_status["status"], 
                        model_status.get("metadata")
                    ):
                        self.logger.info(f"模型状态已同步到主缓存: {model_name}")
                    else:
                        self.logger.error(f"模型状态同步到主缓存失败: {model_name}")
            
            self.logger.info("备用缓存到主缓存数据同步完成")
            
        except Exception as e:
            self.logger.error(f"备用缓存到主缓存同步异常: {e}")
    
    def force_sync(self, direction: str = "primary_to_backup") -> Dict[str, Any]:
        """强制执行数据同步
        
        Args:
            direction: 同步方向，"primary_to_backup" 或 "backup_to_primary"
            
        Returns:
            Dict[str, Any]: 同步结果
        """
        sync_result = {
            "direction": direction,
            "success": False,
            "timestamp": datetime.now().isoformat(),
            "error": None
        }
        
        try:
            if direction == "primary_to_backup":
                if self._primary_cache and self._backup_cache:
                    self._sync_primary_to_backup()
                    sync_result["success"] = True
                else:
                    sync_result["error"] = "主缓存或备用缓存不可用"
            elif direction == "backup_to_primary":
                if self._primary_cache and self._backup_cache:
                    self._sync_backup_to_primary()
                    sync_result["success"] = True
                else:
                    sync_result["error"] = "主缓存或备用缓存不可用"
            else:
                sync_result["error"] = f"不支持的同步方向: {direction}"
                
        except Exception as e:
            sync_result["error"] = str(e)
            self.logger.error(f"强制同步异常: {e}")
        
        return sync_result
    
    def shutdown(self) -> None:
        """关闭所有缓存连接"""
        try:
            if self._primary_cache:
                self._primary_cache.disconnect()
                self.logger.info("Redis主缓存已断开")
            
            if self._backup_cache:
                self._backup_cache.disconnect()
                self.logger.info("SQLite备用缓存已断开")
            
            self._current_cache = None
            self.logger.info("缓存工厂已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭缓存工厂时发生异常: {e}")


# 全局缓存工厂实例
cache_factory = CacheFactory()


def get_cache() -> Optional[CacheInterface]:
    """获取全局缓存实例的便捷函数
    
    Returns:
        Optional[CacheInterface]: 可用的缓存实例
    """
    return cache_factory.get_cache()


def initialize_cache(config: Dict[str, Any]) -> bool:
    """初始化全局缓存系统的便捷函数
    
    Args:
        config: 系统配置字典
        
    Returns:
        bool: 初始化成功返回True，失败返回False
    """
    return cache_factory.initialize(config)