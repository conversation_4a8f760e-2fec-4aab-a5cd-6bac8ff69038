"""
数据层模块

该模块提供缓存系统的实现，包括Redis主缓存和SQLite备用缓存。
严格遵循Nautilus Trader数据格式规范，确保与回测引擎完全兼容。
"""

from .cache_interface import CacheInterface, BTCPrediction
from .redis_cache import RedisCache
from .sqlite_cache import SQLiteCache
from .cache_factory import CacheFactory, cache_factory, get_cache, initialize_cache

__all__ = [
    "CacheInterface",
    "BTCPrediction", 
    "RedisCache",
    "SQLiteCache",
    "CacheFactory",
    "cache_factory",
    "get_cache",
    "initialize_cache"
]