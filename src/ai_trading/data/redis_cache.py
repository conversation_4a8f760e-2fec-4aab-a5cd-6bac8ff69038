"""
Redis缓存实现

该模块实现了基于Redis的缓存系统，严格遵循Nautilus Trader数据格式规范。
使用Nautilus官方序列化器处理Bar对象，确保与回测引擎完全兼容。
"""

import json
import time
import logging
import pickle
import base64
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta, timezone
import redis
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from nautilus_trader.model.data import Bar

from .cache_interface import CacheInterface, BTCPrediction


class RedisCache(CacheInterface):
    """Redis缓存实现类
    
    严格遵循Nautilus Trader数据格式规范，使用官方序列化器处理Bar对象。
    支持连接池、自动重连、健康检查等生产级功能。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Redis缓存客户端
        
        Args:
            config: Redis配置字典，包含host、port、db、password等参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._client: Optional[redis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
        
        # 缓存键名常量
        self.BTC_PRED_KEY = "btc_pred:latest"
        self.MODEL_STATUS_PREFIX = "model_status:"
        self.NAUTILUS_BARS_PREFIX = "nautilus_bars:"
        
        # 连接参数
        self.host = config.get("host", "localhost")
        self.port = config.get("port", 6379)
        self.db = config.get("db", 0)
        self.password = config.get("password")
        self.connection_timeout = config.get("connection_timeout", 5)
        self.max_connections = config.get("max_connections", 10)
        
        self.logger.info(f"初始化Redis缓存客户端: {self.host}:{self.port}/{self.db}")
    
    def connect(self) -> bool:
        """连接到Redis服务器
        
        创建连接池并测试连接，支持密码认证和超时设置。
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            # 创建连接池
            self._connection_pool = redis.ConnectionPool(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                socket_timeout=self.connection_timeout,
                socket_connect_timeout=self.connection_timeout,
                max_connections=self.max_connections,
                decode_responses=False,  # 保持二进制数据用于Nautilus序列化
                retry_on_timeout=True
            )
            
            # 创建Redis客户端
            self._client = redis.Redis(connection_pool=self._connection_pool)
            
            # 测试连接
            self._client.ping()
            
            self.logger.info("Redis连接成功建立")
            return True
            
        except (ConnectionError, TimeoutError) as e:
            self.logger.error(f"Redis连接失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Redis连接异常: {e}")
            return False
    
    def disconnect(self) -> None:
        """断开Redis连接"""
        try:
            if self._client:
                self._client.close()
                self._client = None
            
            if self._connection_pool:
                self._connection_pool.disconnect()
                self._connection_pool = None
                
            self.logger.info("Redis连接已断开")
            
        except Exception as e:
            self.logger.error(f"断开Redis连接时发生异常: {e}")
    
    def is_connected(self) -> bool:
        """检查Redis连接状态
        
        Returns:
            bool: 连接正常返回True，否则返回False
        """
        try:
            if not self._client:
                return False
            
            # 使用ping命令测试连接
            self._client.ping()
            return True
            
        except (ConnectionError, TimeoutError, RedisError):
            return False
        except Exception as e:
            self.logger.error(f"检查Redis连接状态时发生异常: {e}")
            return False
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值
        
        Args:
            key: 缓存键名
            value: 缓存值（将被JSON序列化）
            ttl: 过期时间（秒），None表示永不过期
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法设置缓存")
                return False
            
            # JSON序列化值
            serialized_value = json.dumps(value, ensure_ascii=False, default=str)
            
            # 设置缓存
            if ttl:
                result = self._client.setex(key, ttl, serialized_value)
            else:
                result = self._client.set(key, serialized_value)
            
            if result:
                self.logger.debug(f"成功设置缓存: {key}")
                return True
            else:
                self.logger.error(f"设置缓存失败: {key}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置缓存时发生异常 {key}: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键名
            
        Returns:
            Optional[Any]: 缓存值，不存在或过期返回None
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法获取缓存")
                return None
            
            # 获取原始数据
            raw_value = self._client.get(key)
            if raw_value is None:
                return None
            
            # JSON反序列化
            value = json.loads(raw_value.decode('utf-8'))
            self.logger.debug(f"成功获取缓存: {key}")
            return value
            
        except json.JSONDecodeError as e:
            self.logger.error(f"缓存值JSON解析失败 {key}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取缓存时发生异常 {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """删除缓存项
        
        Args:
            key: 缓存键名
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法删除缓存")
                return False
            
            result = self._client.delete(key)
            if result > 0:
                self.logger.debug(f"成功删除缓存: {key}")
                return True
            else:
                self.logger.debug(f"缓存不存在，无需删除: {key}")
                return True
                
        except Exception as e:
            self.logger.error(f"删除缓存时发生异常 {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查键是否存在
        
        Args:
            key: 缓存键名
            
        Returns:
            bool: 存在返回True，不存在返回False
        """
        try:
            if not self._client or not self.is_connected():
                return False
            
            return bool(self._client.exists(key))
            
        except Exception as e:
            self.logger.error(f"检查缓存存在性时发生异常 {key}: {e}")
            return False
    
    def set_nautilus_bars(self, key: str, bars: List[Bar], ttl: Optional[int] = None) -> bool:
        """存储Nautilus Bar对象列表
        
        使用Nautilus官方MsgPack序列化器存储Bar对象，确保与回测引擎兼容。
        
        Args:
            key: 缓存键名
            bars: Nautilus Bar对象列表
            ttl: 过期时间（秒）
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法存储Nautilus Bar数据")
                return False
            
            if not bars:
                self.logger.warning(f"Bar列表为空，跳过存储: {key}")
                return True
            
            # 使用pickle序列化Bar对象并转换为base64字符串
            serialized_bars = []
            for bar in bars:
                try:
                    # 使用pickle序列化Bar对象
                    pickled_bar = pickle.dumps(bar)
                    # 转换为base64字符串以便JSON存储
                    base64_bar = base64.b64encode(pickled_bar).decode('utf-8')
                    serialized_bars.append(base64_bar)
                except Exception as e:
                    self.logger.error(f"序列化Bar对象失败: {e}")
                    return False
            
            # 构建完整的缓存键名
            full_key = f"{self.NAUTILUS_BARS_PREFIX}{key}"
            
            # 存储序列化后的数据
            cache_data = {
                "bars": serialized_bars,
                "count": len(bars),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "serializer_version": "pickle"
            }
            
            serialized_data = json.dumps(cache_data, ensure_ascii=False)
            
            # 设置缓存
            if ttl:
                result = self._client.setex(full_key, ttl, serialized_data)
            else:
                result = self._client.set(full_key, serialized_data)
            
            if result:
                self.logger.info(f"成功存储{len(bars)}个Nautilus Bar对象: {full_key}")
                return True
            else:
                self.logger.error(f"存储Nautilus Bar对象失败: {full_key}")
                return False
                
        except Exception as e:
            self.logger.error(f"存储Nautilus Bar对象时发生异常 {key}: {e}")
            return False
    
    def get_nautilus_bars(self, key: str) -> Optional[List[Bar]]:
        """获取Nautilus Bar对象列表
        
        使用Nautilus官方MsgPack反序列化器读取Bar对象。
        
        Args:
            key: 缓存键名
            
        Returns:
            Optional[List[Bar]]: Bar对象列表，不存在返回None
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法获取Nautilus Bar数据")
                return None
            
            # 构建完整的缓存键名
            full_key = f"{self.NAUTILUS_BARS_PREFIX}{key}"
            
            # 获取原始数据
            raw_data = self._client.get(full_key)
            if raw_data is None:
                self.logger.debug(f"Nautilus Bar缓存不存在: {full_key}")
                return None
            
            # JSON反序列化
            cache_data = json.loads(raw_data.decode('utf-8'))
            serialized_bars = cache_data.get("bars", [])
            
            if not serialized_bars:
                self.logger.warning(f"缓存中Bar列表为空: {full_key}")
                return []
            
            # 使用pickle反序列化Bar对象
            bars = []
            for base64_bar in serialized_bars:
                try:
                    # 从base64字符串解码
                    pickled_bar = base64.b64decode(base64_bar.encode('utf-8'))
                    # 使用pickle反序列化Bar对象
                    bar = pickle.loads(pickled_bar)
                    bars.append(bar)
                except Exception as e:
                    self.logger.error(f"反序列化Bar对象失败: {e}")
                    return None
            
            self.logger.info(f"成功获取{len(bars)}个Nautilus Bar对象: {full_key}")
            return bars
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Nautilus Bar缓存JSON解析失败 {key}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取Nautilus Bar对象时发生异常 {key}: {e}")
            return None
    
    def set_btc_prediction(self, prediction: BTCPrediction) -> bool:
        """存储BTC预测结果
        
        固定使用键名 'btc_pred:latest' 存储最新的BTC预测结果，TTL设为65分钟。
        
        Args:
            prediction: BTC预测结果对象
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法存储BTC预测结果")
                return False
            
            # 转换为字典格式
            prediction_data = prediction.to_dict()
            
            # 添加存储时间戳
            prediction_data["stored_at"] = datetime.now(timezone.utc).isoformat()
            
            # JSON序列化
            serialized_data = json.dumps(prediction_data, ensure_ascii=False)
            
            # 设置缓存，TTL为65分钟（3900秒）
            result = self._client.setex(self.BTC_PRED_KEY, 3900, serialized_data)
            
            if result:
                self.logger.info(f"成功存储BTC预测结果: {prediction.direction}, 置信度: {prediction.confidence}")
                return True
            else:
                self.logger.error("存储BTC预测结果失败")
                return False
                
        except Exception as e:
            self.logger.error(f"存储BTC预测结果时发生异常: {e}")
            return False
    
    def get_btc_prediction(self) -> Optional[BTCPrediction]:
        """获取最新BTC预测结果
        
        从键名 'btc_pred:latest' 读取最新的BTC预测结果。
        
        Returns:
            Optional[BTCPrediction]: BTC预测结果，不存在或过期返回None
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法获取BTC预测结果")
                return None
            
            # 获取原始数据
            raw_data = self._client.get(self.BTC_PRED_KEY)
            if raw_data is None:
                self.logger.debug("BTC预测结果缓存不存在或已过期")
                return None
            
            # JSON反序列化
            prediction_data = json.loads(raw_data.decode('utf-8'))
            
            # 创建BTCPrediction对象
            prediction = BTCPrediction.from_dict(prediction_data)
            
            # 检查预测是否仍然有效
            if datetime.now(timezone.utc) > prediction.valid_until.replace(tzinfo=timezone.utc):
                self.logger.warning("BTC预测结果已过期")
                # 删除过期的预测
                self.delete(self.BTC_PRED_KEY)
                return None
            
            self.logger.debug(f"成功获取BTC预测结果: {prediction.direction}, 置信度: {prediction.confidence}")
            return prediction
            
        except json.JSONDecodeError as e:
            self.logger.error(f"BTC预测结果JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取BTC预测结果时发生异常: {e}")
            return None
    
    def set_model_status(self, model_name: str, status: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """设置AI模型状态
        
        Args:
            model_name: 模型名称（如 'btc_predictor', 'trading_ai'）
            status: 模型状态（如 'available', 'unavailable', 'loading'）
            metadata: 额外的状态信息
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法设置模型状态")
                return False
            
            # 构建状态数据
            status_data = {
                "status": status,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metadata": metadata or {}
            }
            
            # 构建缓存键名
            key = f"{self.MODEL_STATUS_PREFIX}{model_name}"
            
            # 存储状态数据
            serialized_data = json.dumps(status_data, ensure_ascii=False)
            result = self._client.set(key, serialized_data)
            
            if result:
                self.logger.info(f"成功设置模型状态 {model_name}: {status}")
                return True
            else:
                self.logger.error(f"设置模型状态失败 {model_name}: {status}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置模型状态时发生异常 {model_name}: {e}")
            return False
    
    def get_model_status(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取AI模型状态
        
        Args:
            model_name: 模型名称
            
        Returns:
            Optional[Dict[str, Any]]: 模型状态信息，不存在返回None
        """
        try:
            if not self._client or not self.is_connected():
                self.logger.error("Redis未连接，无法获取模型状态")
                return None
            
            # 构建缓存键名
            key = f"{self.MODEL_STATUS_PREFIX}{model_name}"
            
            # 获取状态数据
            raw_data = self._client.get(key)
            if raw_data is None:
                self.logger.debug(f"模型状态不存在: {model_name}")
                return None
            
            # JSON反序列化
            status_data = json.loads(raw_data.decode('utf-8'))
            
            self.logger.debug(f"成功获取模型状态 {model_name}: {status_data['status']}")
            return status_data
            
        except json.JSONDecodeError as e:
            self.logger.error(f"模型状态JSON解析失败 {model_name}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取模型状态时发生异常 {model_name}: {e}")
            return None
    
    def health_check(self) -> Dict[str, Any]:
        """Redis缓存系统健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息，包含连接状态、延迟、错误信息等
        """
        health_info = {
            "service": "redis_cache",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "unknown",
            "connected": False,
            "latency_ms": None,
            "memory_usage": None,
            "error": None
        }
        
        try:
            if not self._client:
                health_info["status"] = "disconnected"
                health_info["error"] = "Redis客户端未初始化"
                return health_info
            
            # 测试连接并计算延迟
            start_time = time.time()
            self._client.ping()
            latency = (time.time() - start_time) * 1000  # 转换为毫秒
            
            health_info["connected"] = True
            health_info["latency_ms"] = round(latency, 2)
            
            # 获取Redis服务器信息
            info = self._client.info()
            health_info["memory_usage"] = info.get("used_memory_human", "unknown")
            
            # 根据延迟判断健康状态
            if latency < 10:
                health_info["status"] = "healthy"
            elif latency < 50:
                health_info["status"] = "warning"
            else:
                health_info["status"] = "slow"
                
        except (ConnectionError, TimeoutError) as e:
            health_info["status"] = "disconnected"
            health_info["error"] = f"连接错误: {str(e)}"
        except Exception as e:
            health_info["status"] = "error"
            health_info["error"] = f"健康检查异常: {str(e)}"
        
        return health_info