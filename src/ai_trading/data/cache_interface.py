"""
缓存接口抽象基类

该模块定义了缓存系统的抽象接口，确保Redis和SQLite缓存实现的一致性。
严格遵循Nautilus Trader数据格式规范。
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from nautilus_trader.model.data import Bar


@dataclass
class BTCPrediction:
    """BTC预测结果数据类"""
    direction: str  # "bullish", "bearish", "sideways"
    confidence: float  # 0.0 - 1.0
    timestamp: datetime
    valid_until: datetime
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于JSON序列化"""
        return {
            "direction": self.direction,
            "confidence": self.confidence,
            "timestamp": self.timestamp.isoformat(),
            "valid_until": self.valid_until.isoformat(),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BTCPrediction':
        """从字典创建BTCPrediction对象"""
        return cls(
            direction=data["direction"],
            confidence=data["confidence"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            valid_until=datetime.fromisoformat(data["valid_until"]),
            metadata=data["metadata"]
        )


class CacheInterface(ABC):
    """缓存接口抽象基类
    
    定义了缓存系统必须实现的基础方法，确保Redis和SQLite实现的一致性。
    所有实现必须严格遵循Nautilus Trader数据格式规范。
    """
    
    @abstractmethod
    def connect(self) -> bool:
        """连接到缓存系统
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开缓存连接"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态
        
        Returns:
            bool: 连接正常返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值
        
        Args:
            key: 缓存键名
            value: 缓存值
            ttl: 过期时间（秒），None表示永不过期
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键名
            
        Returns:
            Optional[Any]: 缓存值，不存在或过期返回None
        """
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存项
        
        Args:
            key: 缓存键名
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查键是否存在
        
        Args:
            key: 缓存键名
            
        Returns:
            bool: 存在返回True，不存在返回False
        """
        pass
    
    @abstractmethod
    def set_nautilus_bars(self, key: str, bars: List[Bar], ttl: Optional[int] = None) -> bool:
        """存储Nautilus Bar对象列表
        
        使用Nautilus官方序列化格式存储Bar对象，确保与回测引擎兼容。
        
        Args:
            key: 缓存键名
            bars: Nautilus Bar对象列表
            ttl: 过期时间（秒）
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def get_nautilus_bars(self, key: str) -> Optional[List[Bar]]:
        """获取Nautilus Bar对象列表
        
        使用Nautilus官方反序列化格式读取Bar对象。
        
        Args:
            key: 缓存键名
            
        Returns:
            Optional[List[Bar]]: Bar对象列表，不存在返回None
        """
        pass
    
    @abstractmethod
    def set_btc_prediction(self, prediction: BTCPrediction) -> bool:
        """存储BTC预测结果
        
        固定使用键名 'btc_pred:latest' 存储最新的BTC预测结果。
        
        Args:
            prediction: BTC预测结果对象
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def get_btc_prediction(self) -> Optional[BTCPrediction]:
        """获取最新BTC预测结果
        
        从键名 'btc_pred:latest' 读取最新的BTC预测结果。
        
        Returns:
            Optional[BTCPrediction]: BTC预测结果，不存在或过期返回None
        """
        pass
    
    @abstractmethod
    def set_model_status(self, model_name: str, status: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """设置AI模型状态
        
        Args:
            model_name: 模型名称（如 'btc_predictor', 'trading_ai'）
            status: 模型状态（如 'available', 'unavailable', 'loading'）
            metadata: 额外的状态信息
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def get_model_status(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取AI模型状态
        
        Args:
            model_name: 模型名称
            
        Returns:
            Optional[Dict[str, Any]]: 模型状态信息，不存在返回None
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """缓存系统健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息，包含连接状态、延迟、错误信息等
        """
        pass