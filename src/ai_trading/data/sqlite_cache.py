"""
SQLite本地缓存实现

该模块实现了基于SQLite的本地缓存系统，作为Redis的备用缓存。
严格遵循Nautilus Trader数据格式规范，与Redis缓存保持完全相同的接口。
支持数据同步、故障切换、自动清理等功能。
"""

import json
import time
import logging
import pickle
import base64
import sqlite3
import threading
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta, timezone
from pathlib import Path

from nautilus_trader.model.data import Bar

from .cache_interface import CacheInterface, BTCPrediction


class SQLiteCache(CacheInterface):
    """SQLite本地缓存实现类
    
    作为Redis的备用缓存，严格遵循相同的接口规范。
    使用SQLite数据库提供持久化存储，支持TTL过期机制。
    线程安全设计，支持并发访问。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化SQLite缓存
        
        Args:
            config: SQLite配置字典，包含数据库路径等参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._connection: Optional[sqlite3.Connection] = None
        self._lock = threading.RLock()  # 使用可重入锁确保线程安全
        
        # 缓存键名常量（与Redis保持一致）
        self.BTC_PRED_KEY = "btc_pred:latest"
        self.MODEL_STATUS_PREFIX = "model_status:"
        self.NAUTILUS_BARS_PREFIX = "nautilus_bars:"
        
        # 数据库配置
        self.db_path = config.get("db_path", "data/cache.db")
        self.timeout = config.get("timeout", 30.0)
        self.check_same_thread = config.get("check_same_thread", False)
        
        # 确保数据库目录存在
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"初始化SQLite缓存: {self.db_path}")
    
    def connect(self) -> bool:
        """连接到SQLite数据库
        
        创建数据库连接并初始化表结构。
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            with self._lock:
                # 创建数据库连接
                self._connection = sqlite3.connect(
                    self.db_path,
                    timeout=self.timeout,
                    check_same_thread=self.check_same_thread,
                    isolation_level=None  # 自动提交模式
                )
                
                # 设置连接参数
                self._connection.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
                self._connection.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
                self._connection.execute("PRAGMA temp_store=MEMORY")  # 临时数据存储在内存中
                self._connection.execute("PRAGMA cache_size=10000")  # 增加缓存大小
                
                # 创建表结构
                self._create_tables()
                
                # 清理过期数据
                self._cleanup_expired_data()
                
                self.logger.info("SQLite缓存连接成功建立")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"SQLite连接失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"SQLite连接异常: {e}")
            return False
    
    def _create_tables(self) -> None:
        """创建数据库表结构"""
        try:
            # 通用缓存表
            self._connection.execute("""
                CREATE TABLE IF NOT EXISTS cache_data (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    expires_at INTEGER,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL
                )
            """)
            
            # Nautilus Bar数据表
            self._connection.execute("""
                CREATE TABLE IF NOT EXISTS nautilus_bars (
                    key TEXT PRIMARY KEY,
                    bars_data TEXT NOT NULL,
                    bar_count INTEGER NOT NULL,
                    expires_at INTEGER,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    serializer_version TEXT DEFAULT 'pickle'
                )
            """)
            
            # BTC预测结果表
            self._connection.execute("""
                CREATE TABLE IF NOT EXISTS btc_predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    direction TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    valid_until TEXT NOT NULL,
                    metadata TEXT,
                    stored_at TEXT NOT NULL,
                    expires_at INTEGER NOT NULL,
                    created_at INTEGER NOT NULL
                )
            """)
            
            # AI模型状态表
            self._connection.execute("""
                CREATE TABLE IF NOT EXISTS model_status (
                    model_name TEXT PRIMARY KEY,
                    status TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    metadata TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL
                )
            """)
            
            # 创建索引提高查询性能
            self._connection.execute("CREATE INDEX IF NOT EXISTS idx_cache_expires ON cache_data(expires_at)")
            self._connection.execute("CREATE INDEX IF NOT EXISTS idx_bars_expires ON nautilus_bars(expires_at)")
            self._connection.execute("CREATE INDEX IF NOT EXISTS idx_pred_expires ON btc_predictions(expires_at)")
            
            self.logger.debug("SQLite表结构创建完成")
            
        except sqlite3.Error as e:
            self.logger.error(f"创建SQLite表结构失败: {e}")
            raise
    
    def _cleanup_expired_data(self) -> None:
        """清理过期数据"""
        try:
            current_timestamp = int(time.time())
            
            # 清理过期的通用缓存
            self._connection.execute(
                "DELETE FROM cache_data WHERE expires_at IS NOT NULL AND expires_at < ?",
                (current_timestamp,)
            )
            
            # 清理过期的Nautilus Bar数据
            self._connection.execute(
                "DELETE FROM nautilus_bars WHERE expires_at IS NOT NULL AND expires_at < ?",
                (current_timestamp,)
            )
            
            # 清理过期的BTC预测
            self._connection.execute(
                "DELETE FROM btc_predictions WHERE expires_at < ?",
                (current_timestamp,)
            )
            
            self.logger.debug("过期数据清理完成")
            
        except sqlite3.Error as e:
            self.logger.error(f"清理过期数据失败: {e}")
    
    def disconnect(self) -> None:
        """断开SQLite连接"""
        try:
            with self._lock:
                if self._connection:
                    self._connection.close()
                    self._connection = None
                    
                self.logger.info("SQLite连接已断开")
                
        except Exception as e:
            self.logger.error(f"断开SQLite连接时发生异常: {e}")
    
    def is_connected(self) -> bool:
        """检查SQLite连接状态
        
        Returns:
            bool: 连接正常返回True，否则返回False
        """
        try:
            with self._lock:
                if not self._connection:
                    return False
                
                # 执行简单查询测试连接
                self._connection.execute("SELECT 1").fetchone()
                return True
                
        except sqlite3.Error:
            return False
        except Exception as e:
            self.logger.error(f"检查SQLite连接状态时发生异常: {e}")
            return False
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值
        
        Args:
            key: 缓存键名
            value: 缓存值（将被JSON序列化）
            ttl: 过期时间（秒），None表示永不过期
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法设置缓存")
                    return False
                
                # JSON序列化值
                serialized_value = json.dumps(value, ensure_ascii=False, default=str)
                
                # 计算过期时间戳
                expires_at = None
                if ttl:
                    expires_at = int(time.time()) + ttl
                
                current_timestamp = int(time.time())
                
                # 使用REPLACE语句插入或更新数据
                self._connection.execute("""
                    REPLACE INTO cache_data (key, value, expires_at, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (key, serialized_value, expires_at, current_timestamp, current_timestamp))
                
                self.logger.debug(f"成功设置缓存: {key}")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"设置缓存时发生SQLite错误 {key}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"设置缓存时发生异常 {key}: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键名
            
        Returns:
            Optional[Any]: 缓存值，不存在或过期返回None
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法获取缓存")
                    return None
                
                current_timestamp = int(time.time())
                
                # 查询数据，同时检查过期时间
                cursor = self._connection.execute("""
                    SELECT value FROM cache_data 
                    WHERE key = ? AND (expires_at IS NULL OR expires_at > ?)
                """, (key, current_timestamp))
                
                row = cursor.fetchone()
                if row is None:
                    return None
                
                # JSON反序列化
                value = json.loads(row[0])
                self.logger.debug(f"成功获取缓存: {key}")
                return value
                
        except json.JSONDecodeError as e:
            self.logger.error(f"缓存值JSON解析失败 {key}: {e}")
            return None
        except sqlite3.Error as e:
            self.logger.error(f"获取缓存时发生SQLite错误 {key}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取缓存时发生异常 {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """删除缓存项
        
        Args:
            key: 缓存键名
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法删除缓存")
                    return False
                
                cursor = self._connection.execute("DELETE FROM cache_data WHERE key = ?", (key,))
                
                if cursor.rowcount > 0:
                    self.logger.debug(f"成功删除缓存: {key}")
                else:
                    self.logger.debug(f"缓存不存在，无需删除: {key}")
                
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"删除缓存时发生SQLite错误 {key}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"删除缓存时发生异常 {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查键是否存在
        
        Args:
            key: 缓存键名
            
        Returns:
            bool: 存在返回True，不存在返回False
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    return False
                
                current_timestamp = int(time.time())
                
                cursor = self._connection.execute("""
                    SELECT 1 FROM cache_data 
                    WHERE key = ? AND (expires_at IS NULL OR expires_at > ?)
                """, (key, current_timestamp))
                
                return cursor.fetchone() is not None
                
        except sqlite3.Error as e:
            self.logger.error(f"检查缓存存在性时发生SQLite错误 {key}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"检查缓存存在性时发生异常 {key}: {e}")
            return False    

    def set_nautilus_bars(self, key: str, bars: List[Bar], ttl: Optional[int] = None) -> bool:
        """存储Nautilus Bar对象列表
        
        使用pickle序列化Bar对象，确保与Redis实现保持一致。
        
        Args:
            key: 缓存键名
            bars: Nautilus Bar对象列表
            ttl: 过期时间（秒）
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法存储Nautilus Bar数据")
                    return False
                
                if not bars:
                    self.logger.warning(f"Bar列表为空，跳过存储: {key}")
                    return True
                
                # 使用pickle序列化Bar对象并转换为base64字符串
                serialized_bars = []
                for bar in bars:
                    try:
                        # 使用pickle序列化Bar对象
                        pickled_bar = pickle.dumps(bar)
                        # 转换为base64字符串以便JSON存储
                        base64_bar = base64.b64encode(pickled_bar).decode('utf-8')
                        serialized_bars.append(base64_bar)
                    except Exception as e:
                        self.logger.error(f"序列化Bar对象失败: {e}")
                        return False
                
                # 构建缓存数据
                cache_data = {
                    "bars": serialized_bars,
                    "count": len(bars),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "serializer_version": "pickle"
                }
                
                serialized_data = json.dumps(cache_data, ensure_ascii=False)
                
                # 计算过期时间戳
                expires_at = None
                if ttl:
                    expires_at = int(time.time()) + ttl
                
                current_timestamp = int(time.time())
                
                # 存储到数据库
                self._connection.execute("""
                    REPLACE INTO nautilus_bars 
                    (key, bars_data, bar_count, expires_at, created_at, updated_at, serializer_version)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (key, serialized_data, len(bars), expires_at, current_timestamp, current_timestamp, "pickle"))
                
                self.logger.info(f"成功存储{len(bars)}个Nautilus Bar对象: {key}")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"存储Nautilus Bar对象时发生SQLite错误 {key}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"存储Nautilus Bar对象时发生异常 {key}: {e}")
            return False
    
    def get_nautilus_bars(self, key: str) -> Optional[List[Bar]]:
        """获取Nautilus Bar对象列表
        
        使用pickle反序列化Bar对象，确保与Redis实现保持一致。
        
        Args:
            key: 缓存键名
            
        Returns:
            Optional[List[Bar]]: Bar对象列表，不存在返回None
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法获取Nautilus Bar数据")
                    return None
                
                current_timestamp = int(time.time())
                
                # 查询数据，同时检查过期时间
                cursor = self._connection.execute("""
                    SELECT bars_data FROM nautilus_bars 
                    WHERE key = ? AND (expires_at IS NULL OR expires_at > ?)
                """, (key, current_timestamp))
                
                row = cursor.fetchone()
                if row is None:
                    self.logger.debug(f"Nautilus Bar缓存不存在: {key}")
                    return None
                
                # JSON反序列化
                cache_data = json.loads(row[0])
                serialized_bars = cache_data.get("bars", [])
                
                if not serialized_bars:
                    self.logger.warning(f"缓存中Bar列表为空: {key}")
                    return []
                
                # 使用pickle反序列化Bar对象
                bars = []
                for base64_bar in serialized_bars:
                    try:
                        # 从base64字符串解码
                        pickled_bar = base64.b64decode(base64_bar.encode('utf-8'))
                        # 使用pickle反序列化Bar对象
                        bar = pickle.loads(pickled_bar)
                        bars.append(bar)
                    except Exception as e:
                        self.logger.error(f"反序列化Bar对象失败: {e}")
                        return None
                
                self.logger.info(f"成功获取{len(bars)}个Nautilus Bar对象: {key}")
                return bars
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Nautilus Bar缓存JSON解析失败 {key}: {e}")
            return None
        except sqlite3.Error as e:
            self.logger.error(f"获取Nautilus Bar对象时发生SQLite错误 {key}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取Nautilus Bar对象时发生异常 {key}: {e}")
            return None
    
    def set_btc_prediction(self, prediction: BTCPrediction) -> bool:
        """存储BTC预测结果
        
        固定使用键名 'btc_pred:latest' 存储最新的BTC预测结果，TTL设为65分钟。
        
        Args:
            prediction: BTC预测结果对象
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法存储BTC预测结果")
                    return False
                
                # 计算过期时间戳（65分钟后）
                expires_at = int(time.time()) + 3900  # 65分钟 = 3900秒
                current_timestamp = int(time.time())
                
                # 存储到专用的BTC预测表
                self._connection.execute("""
                    INSERT INTO btc_predictions 
                    (direction, confidence, timestamp, valid_until, metadata, stored_at, expires_at, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    prediction.direction,
                    prediction.confidence,
                    prediction.timestamp.isoformat(),
                    prediction.valid_until.isoformat(),
                    json.dumps(prediction.metadata, ensure_ascii=False),
                    datetime.now(timezone.utc).isoformat(),
                    expires_at,
                    current_timestamp
                ))
                
                # 同时在通用缓存表中存储（保持与Redis一致的接口）
                prediction_data = prediction.to_dict()
                prediction_data["stored_at"] = datetime.now(timezone.utc).isoformat()
                
                serialized_data = json.dumps(prediction_data, ensure_ascii=False)
                
                self._connection.execute("""
                    REPLACE INTO cache_data (key, value, expires_at, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (self.BTC_PRED_KEY, serialized_data, expires_at, current_timestamp, current_timestamp))
                
                self.logger.info(f"成功存储BTC预测结果: {prediction.direction}, 置信度: {prediction.confidence}")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"存储BTC预测结果时发生SQLite错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"存储BTC预测结果时发生异常: {e}")
            return False
    
    def get_btc_prediction(self) -> Optional[BTCPrediction]:
        """获取最新BTC预测结果
        
        从键名 'btc_pred:latest' 读取最新的BTC预测结果。
        
        Returns:
            Optional[BTCPrediction]: BTC预测结果，不存在或过期返回None
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法获取BTC预测结果")
                    return None
                
                current_timestamp = int(time.time())
                
                # 从通用缓存表查询
                cursor = self._connection.execute("""
                    SELECT value FROM cache_data 
                    WHERE key = ? AND (expires_at IS NULL OR expires_at > ?)
                """, (self.BTC_PRED_KEY, current_timestamp))
                
                row = cursor.fetchone()
                if row is None:
                    self.logger.debug("BTC预测结果缓存不存在或已过期")
                    return None
                
                # JSON反序列化
                prediction_data = json.loads(row[0])
                
                # 创建BTCPrediction对象
                prediction = BTCPrediction.from_dict(prediction_data)
                
                # 检查预测是否仍然有效
                if datetime.now(timezone.utc) > prediction.valid_until.replace(tzinfo=timezone.utc):
                    self.logger.warning("BTC预测结果已过期")
                    # 删除过期的预测
                    self.delete(self.BTC_PRED_KEY)
                    return None
                
                self.logger.debug(f"成功获取BTC预测结果: {prediction.direction}, 置信度: {prediction.confidence}")
                return prediction
                
        except json.JSONDecodeError as e:
            self.logger.error(f"BTC预测结果JSON解析失败: {e}")
            return None
        except sqlite3.Error as e:
            self.logger.error(f"获取BTC预测结果时发生SQLite错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取BTC预测结果时发生异常: {e}")
            return None
    
    def set_model_status(self, model_name: str, status: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """设置AI模型状态
        
        Args:
            model_name: 模型名称（如 'btc_predictor', 'trading_ai'）
            status: 模型状态（如 'available', 'unavailable', 'loading'）
            metadata: 额外的状态信息
            
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法设置模型状态")
                    return False
                
                current_timestamp = int(time.time())
                timestamp_iso = datetime.now(timezone.utc).isoformat()
                metadata_json = json.dumps(metadata or {}, ensure_ascii=False)
                
                # 存储到专用的模型状态表
                self._connection.execute("""
                    REPLACE INTO model_status 
                    (model_name, status, timestamp, metadata, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (model_name, status, timestamp_iso, metadata_json, current_timestamp, current_timestamp))
                
                # 同时在通用缓存表中存储（保持与Redis一致的接口）
                status_data = {
                    "status": status,
                    "timestamp": timestamp_iso,
                    "metadata": metadata or {}
                }
                
                cache_key = f"{self.MODEL_STATUS_PREFIX}{model_name}"
                serialized_data = json.dumps(status_data, ensure_ascii=False)
                
                self._connection.execute("""
                    REPLACE INTO cache_data (key, value, expires_at, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (cache_key, serialized_data, None, current_timestamp, current_timestamp))
                
                self.logger.info(f"成功设置模型状态 {model_name}: {status}")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"设置模型状态时发生SQLite错误 {model_name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"设置模型状态时发生异常 {model_name}: {e}")
            return False
    
    def get_model_status(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取AI模型状态
        
        Args:
            model_name: 模型名称
            
        Returns:
            Optional[Dict[str, Any]]: 模型状态信息，不存在返回None
        """
        try:
            with self._lock:
                if not self._connection or not self.is_connected():
                    self.logger.error("SQLite未连接，无法获取模型状态")
                    return None
                
                # 从通用缓存表查询
                cache_key = f"{self.MODEL_STATUS_PREFIX}{model_name}"
                cursor = self._connection.execute("""
                    SELECT value FROM cache_data WHERE key = ?
                """, (cache_key,))
                
                row = cursor.fetchone()
                if row is None:
                    self.logger.debug(f"模型状态不存在: {model_name}")
                    return None
                
                # JSON反序列化
                status_data = json.loads(row[0])
                
                self.logger.debug(f"成功获取模型状态 {model_name}: {status_data['status']}")
                return status_data
                
        except json.JSONDecodeError as e:
            self.logger.error(f"模型状态JSON解析失败 {model_name}: {e}")
            return None
        except sqlite3.Error as e:
            self.logger.error(f"获取模型状态时发生SQLite错误 {model_name}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取模型状态时发生异常 {model_name}: {e}")
            return None
    
    def health_check(self) -> Dict[str, Any]:
        """SQLite缓存系统健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息，包含连接状态、数据库大小、表统计等
        """
        health_info = {
            "service": "sqlite_cache",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "unknown",
            "connected": False,
            "db_path": self.db_path,
            "db_size_mb": None,
            "table_stats": {},
            "error": None
        }
        
        try:
            with self._lock:
                if not self._connection:
                    health_info["status"] = "disconnected"
                    health_info["error"] = "SQLite连接未初始化"
                    return health_info
                
                # 测试连接
                start_time = time.time()
                self._connection.execute("SELECT 1").fetchone()
                latency = (time.time() - start_time) * 1000  # 转换为毫秒
                
                health_info["connected"] = True
                health_info["latency_ms"] = round(latency, 2)
                
                # 获取数据库文件大小
                try:
                    db_path = Path(self.db_path)
                    if db_path.exists():
                        db_size_bytes = db_path.stat().st_size
                        health_info["db_size_mb"] = round(db_size_bytes / (1024 * 1024), 2)
                except Exception as e:
                    health_info["db_size_mb"] = f"获取失败: {e}"
                
                # 获取表统计信息
                tables = ["cache_data", "nautilus_bars", "btc_predictions", "model_status"]
                for table in tables:
                    try:
                        cursor = self._connection.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        health_info["table_stats"][table] = count
                    except sqlite3.Error as e:
                        health_info["table_stats"][table] = f"查询失败: {e}"
                
                # 根据延迟判断健康状态
                if latency < 5:
                    health_info["status"] = "healthy"
                elif latency < 20:
                    health_info["status"] = "warning"
                else:
                    health_info["status"] = "slow"
                    
        except sqlite3.Error as e:
            health_info["status"] = "error"
            health_info["error"] = f"SQLite错误: {str(e)}"
        except Exception as e:
            health_info["status"] = "error"
            health_info["error"] = f"健康检查异常: {str(e)}"
        
        return health_info
    
    def sync_from_redis(self, redis_cache: 'RedisCache') -> Dict[str, Any]:
        """从Redis同步数据到SQLite
        
        Args:
            redis_cache: Redis缓存实例
            
        Returns:
            Dict[str, Any]: 同步结果统计
        """
        sync_stats = {
            "btc_prediction": False,
            "model_status": {},
            "errors": [],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        try:
            with self._lock:
                if not self.is_connected():
                    sync_stats["errors"].append("SQLite未连接")
                    return sync_stats
                
                if not redis_cache.is_connected():
                    sync_stats["errors"].append("Redis未连接")
                    return sync_stats
                
                # 同步BTC预测结果
                try:
                    btc_prediction = redis_cache.get_btc_prediction()
                    if btc_prediction:
                        if self.set_btc_prediction(btc_prediction):
                            sync_stats["btc_prediction"] = True
                            self.logger.info("BTC预测结果同步成功")
                        else:
                            sync_stats["errors"].append("BTC预测结果同步失败")
                    else:
                        self.logger.debug("Redis中无BTC预测结果需要同步")
                except Exception as e:
                    sync_stats["errors"].append(f"BTC预测同步异常: {e}")
                
                # 同步模型状态
                model_names = ["btc_predictor", "trading_ai"]
                for model_name in model_names:
                    try:
                        model_status = redis_cache.get_model_status(model_name)
                        if model_status:
                            if self.set_model_status(
                                model_name, 
                                model_status["status"], 
                                model_status.get("metadata")
                            ):
                                sync_stats["model_status"][model_name] = True
                                self.logger.info(f"模型状态同步成功: {model_name}")
                            else:
                                sync_stats["model_status"][model_name] = False
                                sync_stats["errors"].append(f"模型状态同步失败: {model_name}")
                        else:
                            self.logger.debug(f"Redis中无模型状态需要同步: {model_name}")
                    except Exception as e:
                        sync_stats["errors"].append(f"模型状态同步异常 {model_name}: {e}")
                
                self.logger.info(f"数据同步完成，成功: {len([k for k, v in sync_stats.items() if v is True])}, 错误: {len(sync_stats['errors'])}")
                
        except Exception as e:
            sync_stats["errors"].append(f"同步过程异常: {e}")
            self.logger.error(f"数据同步异常: {e}")
        
        return sync_stats
    
    def backup_to_file(self, backup_path: str) -> bool:
        """备份SQLite数据库到指定路径
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 备份成功返回True，失败返回False
        """
        try:
            with self._lock:
                if not self.is_connected():
                    self.logger.error("SQLite未连接，无法执行备份")
                    return False
                
                # 确保备份目录存在
                backup_dir = Path(backup_path).parent
                backup_dir.mkdir(parents=True, exist_ok=True)
                
                # 创建备份连接
                backup_conn = sqlite3.connect(backup_path)
                
                # 执行备份
                self._connection.backup(backup_conn)
                backup_conn.close()
                
                self.logger.info(f"数据库备份成功: {backup_path}")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"备份过程异常: {e}")
            return False