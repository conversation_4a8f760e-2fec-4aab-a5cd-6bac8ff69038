"""
日志配置模块

提供结构化日志配置，支持：
- 多级别日志输出
- 文件和控制台输出
- 结构化日志格式
- 时区感知的时间戳
- 日志轮转
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
import structlog
import pytz
from datetime import datetime

from .config import get_config


def setup_logging(log_level: Optional[str] = None, log_dir: Optional[str] = None) -> None:
    """设置系统日志配置
    
    Args:
        log_level: 日志级别，如果为None则从配置文件读取
        log_dir: 日志目录，默认为 data/logs/
    """
    try:
        # 获取配置
        config = get_config()
        
        # 确定日志级别
        if log_level is None:
            log_level = config.system.log_level
        
        # 确定日志目录
        if log_dir is None:
            log_dir = "data/logs"
        
        # 创建日志目录
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
        
        # 配置时区
        timezone = pytz.timezone(config.system.timezone)
        
        # 清除现有的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置日志级别
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        root_logger.setLevel(numeric_level)
        
        # 创建格式化器
        formatter = CustomFormatter(timezone)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器 - 主日志
        main_log_file = log_path / "ai_trading.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = log_path / "ai_trading_error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
        # 配置structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso", utc=False),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer(ensure_ascii=False)
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # 记录日志配置完成
        logger = logging.getLogger(__name__)
        logger.info(f"日志系统初始化完成 - 级别: {log_level}, 目录: {log_path}")
        
    except Exception as e:
        # 如果日志配置失败，使用基本配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        logging.error(f"日志配置失败，使用基本配置: {e}")


class CustomFormatter(logging.Formatter):
    """自定义日志格式化器
    
    支持时区感知的时间戳和彩色输出
    """
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def __init__(self, timezone: pytz.BaseTzInfo):
        """初始化格式化器
        
        Args:
            timezone: 时区对象
        """
        super().__init__()
        self.timezone = timezone
        
        # 基本格式
        self.base_format = "%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s"
        
        # 详细格式（包含文件和行号）
        self.detailed_format = "%(asctime)s | %(levelname)-8s | %(name)-20s | %(filename)s:%(lineno)d | %(message)s"
    
    def format(self, record):
        """格式化日志记录
        
        Args:
            record: 日志记录
            
        Returns:
            str: 格式化后的日志字符串
        """
        # 转换时间到指定时区
        dt = datetime.fromtimestamp(record.created, tz=self.timezone)
        record.asctime = dt.strftime('%Y-%m-%d %H:%M:%S %Z')
        
        # 选择格式
        if record.levelno >= logging.ERROR:
            formatter = logging.Formatter(self.detailed_format)
        else:
            formatter = logging.Formatter(self.base_format)
        
        # 格式化消息
        formatted = formatter.format(record)
        
        # 如果是控制台输出，添加颜色
        if hasattr(record, 'stream') and record.stream == sys.stdout:
            color = self.COLORS.get(record.levelname, '')
            reset = self.COLORS['RESET']
            formatted = f"{color}{formatted}{reset}"
        
        return formatted


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器
    
    Args:
        name: 日志器名称，通常使用 __name__
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)


def get_structured_logger(name: str) -> structlog.BoundLogger:
    """获取结构化日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        structlog.BoundLogger: 结构化日志器实例
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """日志器混入类
    
    为类提供便捷的日志记录功能
    """
    
    @property
    def logger(self) -> logging.Logger:
        """获取类的日志器"""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    @property
    def struct_logger(self) -> structlog.BoundLogger:
        """获取类的结构化日志器"""
        return get_structured_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")


# 性能监控装饰器
def log_performance(func):
    """性能监控装饰器
    
    记录函数执行时间
    """
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(f"{func.__module__}.{func.__name__}")
        start_time = time.perf_counter()
        
        try:
            result = func(*args, **kwargs)
            execution_time = (time.perf_counter() - start_time) * 1000  # 转换为毫秒
            logger.debug(f"函数 {func.__name__} 执行完成，耗时: {execution_time:.2f}ms")
            return result
        except Exception as e:
            execution_time = (time.perf_counter() - start_time) * 1000
            logger.error(f"函数 {func.__name__} 执行失败，耗时: {execution_time:.2f}ms，错误: {e}")
            raise
    
    return wrapper