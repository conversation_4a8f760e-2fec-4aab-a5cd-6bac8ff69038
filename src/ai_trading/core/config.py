"""
配置管理模块

负责加载和管理AI交易系统的配置信息，包括：
- YAML配置文件加载
- 环境变量处理
- 配置验证
- 热重载支持
- 配置变更监听
"""

import os
import yaml
import pytz
import threading
import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from pathlib import Path
import logging
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)


@dataclass
class AIModelConfig:
    """AI模型配置"""
    model_path: str
    version: str
    backup_path: str = ""
    model_type: str = "auto"
    update_interval: int = 3600
    inference_timeout: int = 100
    input_features: list = field(default_factory=list)
    prediction_horizon: Optional[int] = None


@dataclass
class RedisConfig:
    """Redis配置"""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    connection_timeout: int = 5
    max_connections: int = 10


@dataclass
class SQLiteConfig:
    """SQLite配置"""
    db_path: str = "data/ai_trading.db"
    backup_interval: int = 3600


@dataclass
class RiskManagementConfig:
    """风险管理配置"""
    max_trade_risk: float = 0.02
    exposure_limit: float = 0.15
    max_leverage: int = 10
    volatility_threshold: float = 0.1
    leverage_adjustment_threshold: float = 0.5
    circuit_breaker: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StrategyConfig:
    """策略配置"""
    trend_following: Dict[str, Any] = field(default_factory=dict)
    grid_trading: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MonitoringConfig:
    """监控配置"""
    latency_thresholds: Dict[str, int] = field(default_factory=dict)
    performance_metrics: list = field(default_factory=list)


@dataclass
class SystemConfig:
    """系统配置"""
    timezone: str = "Asia/Shanghai"
    log_level: str = "INFO"
    trader_id: str = "AI_TRADER"


@dataclass
class AITradingConfig:
    """AI交易系统完整配置"""
    system: SystemConfig
    ai_models: Dict[str, AIModelConfig]
    redis: RedisConfig
    sqlite: SQLiteConfig
    risk_management: RiskManagementConfig
    strategies: StrategyConfig
    monitoring: MonitoringConfig
    nautilus: Dict[str, Any] = field(default_factory=dict)


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变更监听器"""
    
    def __init__(self, config_manager: 'ConfigManager'):
        """初始化文件监听器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        
    def on_modified(self, event):
        """文件修改事件处理"""
        if not event.is_directory and event.src_path == str(self.config_manager.config_path):
            logger.info(f"检测到配置文件变更: {event.src_path}")
            self.config_manager._handle_file_change()


class ConfigManager:
    """配置管理器
    
    负责加载、验证和管理系统配置
    支持YAML文件加载和环境变量覆盖
    支持配置文件热重载和变更监听
    """
    
    def __init__(self, config_path: Optional[str] = None, enable_hot_reload: bool = True):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 config/ai_trading_config.yaml
            enable_hot_reload: 是否启用热重载功能
        """
        self.config_path = Path(config_path or "config/ai_trading_config.yaml")
        self._config: Optional[AITradingConfig] = None
        self._file_mtime: Optional[float] = None
        self._enable_hot_reload = enable_hot_reload
        self._observer: Optional[Observer] = None
        self._reload_callbacks: List[Callable[[AITradingConfig], None]] = []
        self._lock = threading.RLock()  # 使用可重入锁保证线程安全
        
    def load_config(self) -> AITradingConfig:
        """加载配置文件
        
        Returns:
            AITradingConfig: 完整的系统配置
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
            ValueError: 配置验证失败
        """
        with self._lock:
            try:
                # 检查配置文件是否存在
                if not self.config_path.exists():
                    raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
                
                # 记录文件修改时间用于热重载
                self._file_mtime = self.config_path.stat().st_mtime
                
                # 加载YAML配置
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    raw_config = yaml.safe_load(f)
                
                logger.info(f"成功加载配置文件: {self.config_path}")
                
                # 应用环境变量覆盖
                raw_config = self._apply_env_overrides(raw_config)
                
                # 解析配置
                self._config = self._parse_config(raw_config)
                
                # 验证配置
                self._validate_config(self._config)
                
                logger.info("配置验证通过")
                return self._config
                
            except Exception as e:
                logger.error(f"加载配置失败: {e}")
                raise
    
    def get_config(self) -> AITradingConfig:
        """获取当前配置
        
        Returns:
            AITradingConfig: 当前配置，如果未加载则自动加载
        """
        with self._lock:
            if self._config is None:
                return self.load_config()
            return self._config
    
    def reload_if_changed(self) -> bool:
        """检查配置文件是否有变化，如有变化则重新加载
        
        Returns:
            bool: 是否重新加载了配置
        """
        with self._lock:
            try:
                if not self.config_path.exists():
                    return False
                
                current_mtime = self.config_path.stat().st_mtime
                if self._file_mtime is None or current_mtime > self._file_mtime:
                    logger.info("检测到配置文件变化，重新加载配置")
                    old_config = self._config
                    self.load_config()
                    
                    # 触发变更回调
                    if old_config is not None and self._config != old_config:
                        self._trigger_reload_callbacks()
                    
                    return True
                
                return False
                
            except Exception as e:
                logger.error(f"检查配置文件变化失败: {e}")
                return False
    
    def start_hot_reload(self) -> None:
        """启动配置文件热重载监听
        
        使用文件系统监听器自动检测配置文件变化
        """
        if not self._enable_hot_reload or self._observer is not None:
            return
        
        try:
            self._observer = Observer()
            event_handler = ConfigFileHandler(self)
            
            # 监听配置文件所在目录
            watch_dir = self.config_path.parent
            self._observer.schedule(event_handler, str(watch_dir), recursive=False)
            self._observer.start()
            
            logger.info(f"已启动配置文件热重载监听: {self.config_path}")
            
        except Exception as e:
            logger.error(f"启动热重载监听失败: {e}")
    
    def stop_hot_reload(self) -> None:
        """停止配置文件热重载监听"""
        if self._observer is not None:
            self._observer.stop()
            self._observer.join()
            self._observer = None
            logger.info("已停止配置文件热重载监听")
    
    def add_reload_callback(self, callback: Callable[[AITradingConfig], None]) -> None:
        """添加配置重载回调函数
        
        Args:
            callback: 配置重载时的回调函数，接收新配置作为参数
        """
        if callback not in self._reload_callbacks:
            self._reload_callbacks.append(callback)
            logger.debug(f"已添加配置重载回调: {callback.__name__}")
    
    def remove_reload_callback(self, callback: Callable[[AITradingConfig], None]) -> None:
        """移除配置重载回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self._reload_callbacks:
            self._reload_callbacks.remove(callback)
            logger.debug(f"已移除配置重载回调: {callback.__name__}")
    
    def _handle_file_change(self) -> None:
        """处理配置文件变更事件"""
        # 延迟一小段时间，避免文件正在写入时读取
        time.sleep(0.1)
        
        try:
            with self._lock:
                old_config = self._config
                self.load_config()
                
                # 触发变更回调
                if old_config is not None and self._config != old_config:
                    self._trigger_reload_callbacks()
                    
        except Exception as e:
            logger.error(f"处理配置文件变更失败: {e}")
    
    def _trigger_reload_callbacks(self) -> None:
        """触发所有配置重载回调"""
        if not self._reload_callbacks or self._config is None:
            return
        
        for callback in self._reload_callbacks:
            try:
                callback(self._config)
                logger.debug(f"已触发配置重载回调: {callback.__name__}")
            except Exception as e:
                logger.error(f"配置重载回调执行失败 {callback.__name__}: {e}")
    
    def validate_config_changes(self, new_config: Dict[str, Any]) -> List[str]:
        """验证配置变更的有效性
        
        Args:
            new_config: 新的配置字典
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        errors = []
        
        try:
            # 尝试解析新配置
            parsed_config = self._parse_config(new_config)
            self._validate_config(parsed_config)
            
        except Exception as e:
            errors.append(str(e))
        
        return errors
    
    def backup_config(self, backup_path: Optional[str] = None) -> str:
        """备份当前配置文件
        
        Args:
            backup_path: 备份文件路径，默认为原文件名加时间戳
            
        Returns:
            str: 备份文件路径
        """
        if backup_path is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.config_path}.backup_{timestamp}"
        
        backup_file = Path(backup_path)
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制配置文件
        import shutil
        shutil.copy2(self.config_path, backup_file)
        
        logger.info(f"配置文件已备份到: {backup_file}")
        return str(backup_file)
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_hot_reload()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_hot_reload()
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖
        
        支持的环境变量格式：
        - AI_TRADING_REDIS_HOST: 覆盖 redis.host
        - AI_TRADING_REDIS_PASSWORD: 覆盖 redis.password
        - AI_TRADING_LOG_LEVEL: 覆盖 system.log_level
        - AI_TRADING_TIMEZONE: 覆盖 system.timezone
        
        Args:
            config: 原始配置字典
            
        Returns:
            Dict[str, Any]: 应用环境变量后的配置
        """
        env_mappings = {
            # 系统配置
            'AI_TRADING_LOG_LEVEL': ['system', 'log_level'],
            'AI_TRADING_TRADER_ID': ['system', 'trader_id'],
            'AI_TRADING_TIMEZONE': ['system', 'timezone'],
            
            # Redis配置
            'AI_TRADING_REDIS_HOST': ['redis', 'host'],
            'AI_TRADING_REDIS_PORT': ['redis', 'port'],
            'AI_TRADING_REDIS_DB': ['redis', 'db'],
            'AI_TRADING_REDIS_PASSWORD': ['redis', 'password'],
            'AI_TRADING_REDIS_TIMEOUT': ['redis', 'connection_timeout'],
            
            # SQLite配置
            'AI_TRADING_SQLITE_PATH': ['sqlite', 'db_path'],
            
            # 风险管理配置
            'AI_TRADING_MAX_TRADE_RISK': ['risk_management', 'max_trade_risk'],
            'AI_TRADING_EXPOSURE_LIMIT': ['risk_management', 'exposure_limit'],
            'AI_TRADING_MAX_LEVERAGE': ['risk_management', 'max_leverage'],
            
            # Nautilus配置
            'BINANCE_API_KEY': ['nautilus', 'data_clients', 'binance', 'api_key'],
            'BINANCE_API_SECRET': ['nautilus', 'data_clients', 'binance', 'api_secret'],
            'BINANCE_TESTNET': ['nautilus', 'data_clients', 'binance', 'testnet'],
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # 导航到配置路径，创建不存在的中间节点
                current = config
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                
                # 设置值（处理类型转换）
                final_key = config_path[-1]
                converted_value = self._convert_env_value(final_key, env_value)
                current[final_key] = converted_value
                
                logger.info(f"应用环境变量覆盖: {env_var} -> {'.'.join(config_path)} = {converted_value}")
        
        return config
    
    def _convert_env_value(self, key: str, value: str) -> Any:
        """转换环境变量值为适当的类型
        
        Args:
            key: 配置键名
            value: 环境变量字符串值
            
        Returns:
            Any: 转换后的值
        """
        # 整数类型的配置键
        int_keys = {'port', 'db', 'connection_timeout', 'max_connections', 'max_leverage', 
                   'update_interval', 'inference_timeout', 'backup_interval'}
        
        # 浮点数类型的配置键
        float_keys = {'max_trade_risk', 'exposure_limit', 'volatility_threshold', 
                     'leverage_adjustment_threshold', 'confidence_threshold', 'base_spacing'}
        
        # 布尔类型的配置键
        bool_keys = {'enabled', 'testnet'}
        
        if key in int_keys:
            return int(value)
        elif key in float_keys:
            return float(value)
        elif key in bool_keys:
            return value.lower() in ('true', '1', 'yes', 'on')
        elif value.lower() == 'null' or value.lower() == 'none':
            return None
        else:
            return value
    
    def _parse_config(self, raw_config: Dict[str, Any]) -> AITradingConfig:
        """解析原始配置为结构化配置对象
        
        Args:
            raw_config: 原始配置字典
            
        Returns:
            AITradingConfig: 结构化配置对象
        """
        # 解析系统配置
        system_config = SystemConfig(**raw_config.get('system', {}))
        
        # 解析AI模型配置
        ai_models = {}
        for name, model_config in raw_config.get('ai_models', {}).items():
            ai_models[name] = AIModelConfig(**model_config)
        
        # 解析Redis配置
        redis_config = RedisConfig(**raw_config.get('redis', {}))
        
        # 解析SQLite配置
        sqlite_config = SQLiteConfig(**raw_config.get('sqlite', {}))
        
        # 解析风险管理配置
        risk_config = RiskManagementConfig(**raw_config.get('risk_management', {}))
        
        # 解析策略配置
        strategies_config = StrategyConfig(**raw_config.get('strategies', {}))
        
        # 解析监控配置
        monitoring_config = MonitoringConfig(**raw_config.get('monitoring', {}))
        
        return AITradingConfig(
            system=system_config,
            ai_models=ai_models,
            redis=redis_config,
            sqlite=sqlite_config,
            risk_management=risk_config,
            strategies=strategies_config,
            monitoring=monitoring_config,
            nautilus=raw_config.get('nautilus', {})
        )
    
    def _validate_config(self, config: AITradingConfig) -> None:
        """验证配置的有效性
        
        Args:
            config: 要验证的配置
            
        Raises:
            ValueError: 配置验证失败
        """
        # 验证时区
        try:
            timezone = pytz.timezone(config.system.timezone)
            logger.debug(f"时区验证通过: {config.system.timezone}")
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(f"无效的时区: {config.system.timezone}")
        
        # 验证日志级别
        valid_log_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        if config.system.log_level.upper() not in valid_log_levels:
            raise ValueError(f"无效的日志级别: {config.system.log_level}")
        
        # 验证交易员ID
        if not config.system.trader_id or len(config.system.trader_id.strip()) == 0:
            raise ValueError("trader_id不能为空")
        
        # 验证AI模型配置
        for name, model_config in config.ai_models.items():
            # 检查模型路径
            model_path = Path(model_config.model_path)
            if not model_path.exists():
                logger.warning(f"AI模型文件不存在: {model_config.model_path}")
                # 注意：这里只是警告，不抛出异常，因为模型文件可能在后续阶段添加
            
            # 验证模型版本格式
            if not model_config.version or not model_config.version.strip():
                raise ValueError(f"模型 {name} 的版本号不能为空")
            
            # 验证更新间隔
            if model_config.update_interval <= 0:
                raise ValueError(f"模型 {name} 的更新间隔必须大于0: {model_config.update_interval}")
            
            # 验证推理超时
            if model_config.inference_timeout <= 0:
                raise ValueError(f"模型 {name} 的推理超时必须大于0: {model_config.inference_timeout}")
        
        # 验证Redis配置
        if config.redis.port <= 0 or config.redis.port > 65535:
            raise ValueError(f"Redis端口必须在1-65535范围内: {config.redis.port}")
        
        if config.redis.db < 0:
            raise ValueError(f"Redis数据库索引不能为负数: {config.redis.db}")
        
        if config.redis.connection_timeout <= 0:
            raise ValueError(f"Redis连接超时必须大于0: {config.redis.connection_timeout}")
        
        # 验证SQLite配置
        sqlite_path = Path(config.sqlite.db_path)
        sqlite_dir = sqlite_path.parent
        
        # 创建数据目录
        sqlite_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查目录是否可写
        if not os.access(sqlite_dir, os.W_OK):
            raise ValueError(f"SQLite数据目录不可写: {sqlite_dir}")
        
        if config.sqlite.backup_interval <= 0:
            raise ValueError(f"SQLite备份间隔必须大于0: {config.sqlite.backup_interval}")
        
        # 验证风险管理参数
        if not 0 < config.risk_management.max_trade_risk <= 1:
            raise ValueError(f"max_trade_risk必须在(0,1]范围内: {config.risk_management.max_trade_risk}")
        
        if not 0 < config.risk_management.exposure_limit <= 1:
            raise ValueError(f"exposure_limit必须在(0,1]范围内: {config.risk_management.exposure_limit}")
        
        if config.risk_management.max_leverage <= 0:
            raise ValueError(f"max_leverage必须大于0: {config.risk_management.max_leverage}")
        
        if not 0 < config.risk_management.volatility_threshold <= 1:
            raise ValueError(f"volatility_threshold必须在(0,1]范围内: {config.risk_management.volatility_threshold}")
        
        if config.risk_management.leverage_adjustment_threshold <= 0:
            raise ValueError(f"leverage_adjustment_threshold必须大于0: {config.risk_management.leverage_adjustment_threshold}")
        
        # 验证熔断机制配置
        circuit_breaker = config.risk_management.circuit_breaker
        if 'daily_loss_limit' in circuit_breaker:
            daily_loss_limit = circuit_breaker['daily_loss_limit']
            if not 0 < daily_loss_limit <= 1:
                raise ValueError(f"daily_loss_limit必须在(0,1]范围内: {daily_loss_limit}")
        
        # 验证策略配置
        if hasattr(config.strategies, 'trend_following') and config.strategies.trend_following:
            tf_config = config.strategies.trend_following
            if 'confidence_threshold' in tf_config:
                threshold = tf_config['confidence_threshold']
                if not 0 <= threshold <= 1:
                    raise ValueError(f"趋势跟踪策略的confidence_threshold必须在[0,1]范围内: {threshold}")
        
        if hasattr(config.strategies, 'grid_trading') and config.strategies.grid_trading:
            gt_config = config.strategies.grid_trading
            if 'base_spacing' in gt_config:
                spacing = gt_config['base_spacing']
                if spacing <= 0:
                    raise ValueError(f"网格策略的base_spacing必须大于0: {spacing}")
            
            if 'max_levels' in gt_config:
                levels = gt_config['max_levels']
                if levels <= 0:
                    raise ValueError(f"网格策略的max_levels必须大于0: {levels}")
        
        # 验证监控配置
        if hasattr(config.monitoring, 'latency_thresholds') and config.monitoring.latency_thresholds:
            for coin_type, threshold in config.monitoring.latency_thresholds.items():
                if threshold <= 0:
                    raise ValueError(f"延迟阈值必须大于0: {coin_type}={threshold}")
        
        logger.info("配置验证完成")


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_path: Optional[str] = None) -> ConfigManager:
    """获取全局配置管理器实例
    
    Args:
        config_path: 配置文件路径，仅在首次调用时有效
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
    return _config_manager


def get_config() -> AITradingConfig:
    """获取当前系统配置
    
    Returns:
        AITradingConfig: 当前系统配置
    """
    return get_config_manager().get_config()


class ConfigHelper:
    """配置辅助工具类
    
    提供便捷的配置访问方法
    """
    
    def __init__(self, config: Optional[AITradingConfig] = None):
        """初始化配置辅助工具
        
        Args:
            config: 配置对象，如果为None则使用全局配置
        """
        self._config = config
    
    @property
    def config(self) -> AITradingConfig:
        """获取配置对象"""
        if self._config is None:
            self._config = get_config()
        return self._config
    
    def get_timezone(self) -> pytz.BaseTzInfo:
        """获取系统时区对象
        
        Returns:
            pytz.BaseTzInfo: 时区对象
        """
        return pytz.timezone(self.config.system.timezone)
    
    def get_model_path(self, model_name: str) -> Path:
        """获取AI模型文件路径
        
        Args:
            model_name: 模型名称
            
        Returns:
            Path: 模型文件路径
            
        Raises:
            KeyError: 模型不存在
        """
        if model_name not in self.config.ai_models:
            raise KeyError(f"未找到模型配置: {model_name}")
        
        return Path(self.config.ai_models[model_name].model_path)
    
    def get_model_config(self, model_name: str) -> AIModelConfig:
        """获取AI模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            AIModelConfig: 模型配置
            
        Raises:
            KeyError: 模型不存在
        """
        if model_name not in self.config.ai_models:
            raise KeyError(f"未找到模型配置: {model_name}")
        
        return self.config.ai_models[model_name]
    
    def get_redis_url(self) -> str:
        """获取Redis连接URL
        
        Returns:
            str: Redis连接URL
        """
        redis_config = self.config.redis
        if redis_config.password:
            return f"redis://:{redis_config.password}@{redis_config.host}:{redis_config.port}/{redis_config.db}"
        else:
            return f"redis://{redis_config.host}:{redis_config.port}/{redis_config.db}"
    
    def get_sqlite_path(self) -> Path:
        """获取SQLite数据库路径
        
        Returns:
            Path: SQLite数据库路径
        """
        return Path(self.config.sqlite.db_path)
    
    def is_strategy_enabled(self, strategy_name: str) -> bool:
        """检查策略是否启用
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            bool: 策略是否启用
        """
        strategy_config = getattr(self.config.strategies, strategy_name, None)
        if strategy_config is None:
            return False
        
        return strategy_config.get('enabled', False)
    
    def get_latency_threshold(self, coin_type: str = 'other_coins') -> int:
        """获取延迟阈值
        
        Args:
            coin_type: 币种类型，'mainstream_coins' 或 'other_coins'
            
        Returns:
            int: 延迟阈值（毫秒）
        """
        thresholds = self.config.monitoring.latency_thresholds
        return thresholds.get(coin_type, thresholds.get('other_coins', 300))
    
    def get_circuit_breaker_level(self, volatility: float) -> int:
        """根据波动率获取熔断级别
        
        Args:
            volatility: 当前波动率
            
        Returns:
            int: 熔断级别 (0-3)
        """
        circuit_breaker = self.config.risk_management.circuit_breaker
        volatility_limits = circuit_breaker.get('volatility_limits', {})
        
        if volatility >= volatility_limits.get('level_3', 0.07):
            return 3  # 仅允许平仓
        elif volatility >= volatility_limits.get('level_2', 0.05):
            return 2  # 禁用AI，切换规则引擎
        elif volatility >= volatility_limits.get('level_1', 0.03):
            return 1  # 降低杠杆至5x
        else:
            return 0  # 正常交易
    
    def should_adjust_leverage(self, current_leverage: float, new_leverage: float) -> bool:
        """判断是否应该调整杠杆
        
        Args:
            current_leverage: 当前杠杆
            new_leverage: 新杠杆
            
        Returns:
            bool: 是否应该调整
        """
        threshold = self.config.risk_management.leverage_adjustment_threshold
        return abs(new_leverage - current_leverage) > threshold