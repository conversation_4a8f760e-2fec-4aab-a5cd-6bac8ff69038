"""
Nautilus TradingNode初始化模块

负责初始化和管理Nautilus Trader节点，包括：
- TradingNode配置和构建
- 数据引擎初始化
- 执行引擎初始化
- 风险引擎初始化
- 连接管理
- 错误处理和恢复
"""

import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path
import logging

# Nautilus Trader imports
from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode

from .config import AITradingConfig, get_config
from .logging import LoggerMixin, log_performance


class AITradingNode(LoggerMixin):
    """AI交易系统的Nautilus节点管理器
    
    封装Nautilus TradingNode的初始化和管理逻辑
    提供AI交易系统专用的配置和功能
    """
    
    def __init__(self, config: Optional[AITradingConfig] = None):
        """初始化AI交易节点
        
        Args:
            config: AI交易系统配置，如果为None则自动加载
        """
        self._config = config or get_config()
        self._node: Optional[TradingNode] = None
        self._is_running = False
        self._connection_status = {}
        
        self.logger.info(f"初始化AI交易节点 - Trader ID: {self._config.system.trader_id}")
    
    @property
    def node(self) -> Optional[TradingNode]:
        """获取Nautilus TradingNode实例"""
        return self._node
    
    @property
    def is_running(self) -> bool:
        """检查节点是否正在运行"""
        return self._is_running and self._node is not None
    
    @property
    def data_engine(self):
        """获取数据引擎"""
        if self._node is None:
            raise RuntimeError("节点未初始化，请先调用 build() 方法")
        return self._node.kernel.data_engine
    
    @property
    def exec_engine(self):
        """获取执行引擎"""
        if self._node is None:
            raise RuntimeError("节点未初始化，请先调用 build() 方法")
        return self._node.kernel.exec_engine
    
    @property
    def risk_engine(self):
        """获取风险引擎"""
        if self._node is None:
            raise RuntimeError("节点未初始化，请先调用 build() 方法")
        return self._node.kernel.risk_engine
    
    @property
    def cache(self):
        """获取缓存"""
        if self._node is None:
            raise RuntimeError("节点未初始化，请先调用 build() 方法")
        return self._node.kernel.cache
    
    @property
    def portfolio(self):
        """获取投资组合"""
        if self._node is None:
            raise RuntimeError("节点未初始化，请先调用 build() 方法")
        return self._node.kernel.portfolio
    
    @log_performance
    def build(self) -> None:
        """构建Nautilus TradingNode
        
        根据配置创建和配置TradingNode实例
        
        Raises:
            RuntimeError: 节点构建失败
        """
        try:
            self.logger.info("开始构建Nautilus TradingNode")
            
            # 创建TradingNode配置
            node_config = self._create_node_config()
            
            # 创建TradingNode实例
            self._node = TradingNode(config=node_config)
            
            # 构建节点
            self._node.build()
            
            self.logger.info("Nautilus TradingNode构建完成")
            
        except Exception as e:
            self.logger.error(f"构建Nautilus TradingNode失败: {e}")
            raise RuntimeError(f"节点构建失败: {e}") from e
    
    @log_performance
    async def start(self) -> None:
        """启动交易节点
        
        启动所有引擎和连接
        
        Raises:
            RuntimeError: 节点启动失败
        """
        if self._node is None:
            raise RuntimeError("节点未构建，请先调用 build() 方法")
        
        try:
            self.logger.info("启动AI交易节点")
            
            # 启动节点
            await self._node.start_async()
            
            self._is_running = True
            self.logger.info("AI交易节点启动成功")
            
            # 验证连接状态
            await self._verify_connections()
            
        except Exception as e:
            self.logger.error(f"启动AI交易节点失败: {e}")
            self._is_running = False
            raise RuntimeError(f"节点启动失败: {e}") from e
    
    @log_performance
    async def stop(self) -> None:
        """停止交易节点
        
        优雅地停止所有引擎和连接
        """
        if self._node is None or not self._is_running:
            self.logger.warning("节点未运行，无需停止")
            return
        
        try:
            self.logger.info("停止AI交易节点")
            
            # 停止节点
            await self._node.stop_async()
            
            self._is_running = False
            self.logger.info("AI交易节点已停止")
            
        except Exception as e:
            self.logger.error(f"停止AI交易节点失败: {e}")
            raise
    
    async def restart(self) -> None:
        """重启交易节点"""
        self.logger.info("重启AI交易节点")
        await self.stop()
        await asyncio.sleep(1)  # 等待清理完成
        await self.start()
    
    def _create_node_config(self) -> TradingNodeConfig:
        """创建TradingNode配置
        
        Returns:
            TradingNodeConfig: Nautilus节点配置
        """
        # 确保trader_id包含连字符（Nautilus要求）
        trader_id = self._config.system.trader_id
        if '-' not in trader_id:
            trader_id = f"{trader_id}-001"
        
        # 基础配置字典
        config_dict = {
            "trader_id": trader_id,
            "cache": {
                "database": {
                    "type": "redis",
                    "host": self._config.redis.host,
                    "port": self._config.redis.port,
                    "db": self._config.redis.db,
                }
            },
            "data_clients": {},
            "exec_clients": {},
            "strategies": [],
        }
        
        # 如果Redis有密码，添加密码配置
        if self._config.redis.password:
            config_dict["cache"]["database"]["password"] = self._config.redis.password
        
        # 合并用户自定义的Nautilus配置
        if self._config.nautilus:
            self._merge_config(config_dict, self._config.nautilus)
        
        self.logger.debug(f"创建TradingNode配置: {config_dict}")
        
        return TradingNodeConfig(**config_dict)
    
    def _merge_config(self, base_config: Dict[str, Any], user_config: Dict[str, Any]) -> None:
        """合并用户配置到基础配置
        
        Args:
            base_config: 基础配置字典
            user_config: 用户配置字典
        """
        for key, value in user_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    async def _verify_connections(self) -> None:
        """验证连接状态
        
        检查数据连接和执行连接是否正常
        """
        try:
            self.logger.info("验证连接状态")
            
            # 检查数据引擎状态
            data_engine = self.data_engine
            if data_engine is not None:
                self._connection_status["data_engine"] = "connected"
                self.logger.info("数据引擎连接正常")
            else:
                self._connection_status["data_engine"] = "disconnected"
                self.logger.warning("数据引擎连接异常")
            
            # 检查执行引擎状态
            exec_engine = self.exec_engine
            if exec_engine is not None:
                self._connection_status["exec_engine"] = "connected"
                self.logger.info("执行引擎连接正常")
            else:
                self._connection_status["exec_engine"] = "disconnected"
                self.logger.warning("执行引擎连接异常")
            
            # 检查风险引擎状态
            risk_engine = self.risk_engine
            if risk_engine is not None:
                self._connection_status["risk_engine"] = "connected"
                self.logger.info("风险引擎连接正常")
            else:
                self._connection_status["risk_engine"] = "disconnected"
                self.logger.warning("风险引擎连接异常")
            
            # 记录整体连接状态
            connected_count = sum(1 for status in self._connection_status.values() if status == "connected")
            total_count = len(self._connection_status)
            
            self.logger.info(f"连接验证完成: {connected_count}/{total_count} 个组件连接正常")
            
        except Exception as e:
            self.logger.error(f"验证连接状态失败: {e}")
            raise
    
    def get_connection_status(self) -> Dict[str, str]:
        """获取连接状态
        
        Returns:
            Dict[str, str]: 各组件的连接状态
        """
        return self._connection_status.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查
        
        Returns:
            Dict[str, Any]: 健康检查结果
        """
        health_status = {
            "node_running": self.is_running,
            "connections": self.get_connection_status(),
            "timestamp": self._config.system.timezone,
        }
        
        # 检查各个引擎的状态
        if self._node is not None:
            try:
                health_status["data_engine_status"] = "healthy" if self.data_engine else "unhealthy"
                health_status["exec_engine_status"] = "healthy" if self.exec_engine else "unhealthy"
                health_status["risk_engine_status"] = "healthy" if self.risk_engine else "unhealthy"
                health_status["cache_status"] = "healthy" if self.cache else "unhealthy"
                health_status["portfolio_status"] = "healthy" if self.portfolio else "unhealthy"
            except Exception as e:
                health_status["error"] = str(e)
        
        return health_status


# 全局节点实例
_trading_node: Optional[AITradingNode] = None


def get_trading_node(config: Optional[AITradingConfig] = None) -> AITradingNode:
    """获取全局交易节点实例
    
    Args:
        config: AI交易系统配置，仅在首次调用时有效
        
    Returns:
        AITradingNode: 交易节点实例
    """
    global _trading_node
    if _trading_node is None:
        _trading_node = AITradingNode(config)
    return _trading_node


async def initialize_trading_system(config_path: Optional[str] = None) -> AITradingNode:
    """初始化AI交易系统
    
    完整的系统初始化流程：
    1. 加载配置
    2. 设置日志
    3. 构建节点
    4. 启动节点
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        AITradingNode: 初始化完成的交易节点
        
    Raises:
        RuntimeError: 初始化失败
    """
    try:
        # 导入日志模块（避免循环导入）
        from .logging import setup_logging
        from .config import get_config_manager
        
        # 加载配置
        config_manager = get_config_manager(config_path)
        config = config_manager.load_config()
        
        # 设置日志
        setup_logging(config.system.log_level)
        
        logger = logging.getLogger(__name__)
        logger.info("开始初始化AI交易系统")
        
        # 创建和构建节点
        node = get_trading_node(config)
        node.build()
        
        # 启动节点
        await node.start()
        
        logger.info("AI交易系统初始化完成")
        return node
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"AI交易系统初始化失败: {e}")
        raise RuntimeError(f"系统初始化失败: {e}") from e


async def shutdown_trading_system() -> None:
    """关闭AI交易系统
    
    优雅地关闭所有组件
    """
    global _trading_node
    
    logger = logging.getLogger(__name__)
    logger.info("开始关闭AI交易系统")
    
    if _trading_node is not None:
        try:
            await _trading_node.stop()
            _trading_node = None
            logger.info("AI交易系统已关闭")
        except Exception as e:
            logger.error(f"关闭AI交易系统失败: {e}")
            raise
    else:
        logger.info("AI交易系统未运行，无需关闭")