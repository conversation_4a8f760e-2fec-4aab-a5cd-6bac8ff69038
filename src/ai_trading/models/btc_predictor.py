"""
BTC预测AI模块实现

基于真实的ONNX模型文件实现BTC价格走势预测功能。
包含模型实现和调度器，支持每小时自动预测和手动预测。

模型输入：24个时间步的OHLCV数据和技术指标 (batch_size, 24, 78)
模型输出：3个方向的概率分布 (batch_size, 3) - [看跌, 震荡, 看涨]

主要组件：
- BTCPredictorModel: 核心AI模型实现
"""

import numpy as np
import json
import asyncio
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import pytz

from .base_model import BaseAIModel
from .interfaces import PredictionResult, ModelInfo

logger = logging.getLogger(__name__)


class BTCPredictorModel(BaseAIModel):
    """
    BTC预测AI模型实现类
    
    使用真实的ONNX模型文件进行BTC价格走势预测。
    支持24个时间步的历史数据输入，输出未来走势预测。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化BTC预测模型
        
        Args:
            config: 模型配置参数
        """
        super().__init__(config)
        
        # BTC预测模型特定配置
        self.sequence_length = 24  # 输入序列长度
        self.feature_count = 78    # 特征数量
        self.output_classes = 3    # 输出类别数：[看跌, 震荡, 看涨]
        
        # 类别映射
        self.class_mapping = {
            0: "bearish",   # 看跌
            1: "sideways",  # 震荡
            2: "bullish"    # 看涨
        }
        
        # 默认模型路径
        if not self.model_path:
            self.model_path = "models/btc_predictor/model.onnx"
        
        # 加载模型信息文件
        self._load_model_metadata()
    
    def _load_model_metadata(self) -> None:
        """
        加载模型元数据信息
        """
        try:
            model_dir = Path(self.model_path).parent
            info_file = model_dir / "model_info.json"
            
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    self._model_metadata.update(metadata)
                    logger.info(f"加载模型元数据: {info_file}")
            else:
                logger.warning(f"模型信息文件不存在: {info_file}")
                
        except Exception as e:
            logger.warning(f"加载模型元数据失败: {e}")
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        验证BTC预测模型的输入数据格式
        
        Args:
            data: 输入数据，应包含历史OHLCV数据和技术指标
            
        Returns:
            bool: True表示数据格式正确，False表示格式错误
        """
        # 调用父类基础验证
        if not super().validate_input_data(data):
            return False
        
        # 检查必需的字段
        required_fields = ['ohlcv_data', 'technical_indicators']
        for field in required_fields:
            if field not in data:
                logger.error(f"缺少必需字段: {field}")
                return False
        
        # 验证OHLCV数据格式
        ohlcv_data = data['ohlcv_data']
        if not isinstance(ohlcv_data, (list, np.ndarray)):
            logger.error("ohlcv_data必须是列表或numpy数组")
            return False
        
        # 转换为numpy数组进行形状检查
        try:
            ohlcv_array = np.array(ohlcv_data)
            if ohlcv_array.shape != (self.sequence_length, 5):  # 24个时间步，5个OHLCV特征
                logger.error(f"ohlcv_data形状错误，期望({self.sequence_length}, 5)，实际{ohlcv_array.shape}")
                return False
        except Exception as e:
            logger.error(f"ohlcv_data格式错误: {e}")
            return False
        
        # 验证技术指标数据
        tech_indicators = data['technical_indicators']
        if not isinstance(tech_indicators, (list, np.ndarray)):
            logger.error("technical_indicators必须是列表或numpy数组")
            return False
        
        try:
            tech_array = np.array(tech_indicators)
            expected_tech_features = self.feature_count - 5  # 总特征数减去OHLCV的5个特征
            if tech_array.shape != (self.sequence_length, expected_tech_features):
                logger.error(f"technical_indicators形状错误，期望({self.sequence_length}, {expected_tech_features})，实际{tech_array.shape}")
                return False
        except Exception as e:
            logger.error(f"technical_indicators格式错误: {e}")
            return False
        
        return True
    
    def _preprocess_input(self, data: Dict[str, Any]) -> np.ndarray:
        """
        预处理输入数据为模型所需格式
        
        Args:
            data: 原始输入数据
            
        Returns:
            np.ndarray: 预处理后的输入数据，形状为(1, 24, 78)
        """
        # 获取OHLCV数据
        ohlcv_data = np.array(data['ohlcv_data'], dtype=np.float32)
        
        # 获取技术指标数据
        tech_indicators = np.array(data['technical_indicators'], dtype=np.float32)
        
        # 合并OHLCV和技术指标数据
        combined_features = np.concatenate([ohlcv_data, tech_indicators], axis=1)
        
        # 添加batch维度
        model_input = np.expand_dims(combined_features, axis=0)
        
        logger.debug(f"预处理后的输入形状: {model_input.shape}")
        return model_input
    
    def _postprocess_output(self, model_output: np.ndarray, input_data: Dict[str, Any]) -> PredictionResult:
        """
        后处理模型输出为预测结果
        
        Args:
            model_output: 模型原始输出，形状为(1, 3)
            input_data: 原始输入数据，用于元数据
            
        Returns:
            PredictionResult: 格式化的预测结果
        """
        # 获取概率分布
        probabilities = model_output[0]  # 移除batch维度
        
        # 应用softmax确保概率和为1
        exp_probs = np.exp(probabilities - np.max(probabilities))
        softmax_probs = exp_probs / np.sum(exp_probs)
        
        # 获取最高概率的类别
        predicted_class = np.argmax(softmax_probs)
        confidence = float(softmax_probs[predicted_class])
        direction = self.class_mapping[predicted_class]
        
        # 创建预测结果
        current_time = datetime.now(timezone.utc)
        
        # 计算有效期（假设预测有效期为1小时）
        valid_until = datetime.fromtimestamp(
            current_time.timestamp() + 3600,  # 1小时后
            tz=timezone.utc
        ).isoformat()
        
        result = PredictionResult(
            direction=direction,
            confidence=confidence,
            timestamp=current_time.isoformat(),
            valid_until=valid_until,
            metadata={
                'model_type': 'btc_predictor',
                'model_path': self.model_path,
                'model_version': self.model_version,
                'probabilities': {
                    'bearish': float(softmax_probs[0]),
                    'sideways': float(softmax_probs[1]),
                    'bullish': float(softmax_probs[2])
                },
                'input_sequence_length': self.sequence_length,
                'feature_count': self.feature_count,
                'prediction_horizon': '1h'
            }
        )
        
        logger.info(f"BTC预测结果: {direction} (置信度: {confidence:.3f})")
        return result
    
    def predict(self, data: Dict[str, Any]) -> PredictionResult:
        """
        执行BTC价格走势预测
        
        Args:
            data: 输入数据，包含历史OHLCV数据和技术指标
                  格式：{
                      'ohlcv_data': [[open, high, low, close, volume], ...],  # 24个时间步
                      'technical_indicators': [[rsi, macd, ...], ...],        # 24个时间步的技术指标
                      'timestamp': '2024-01-01T00:00:00Z'                     # 可选的时间戳
                  }
                  
        Returns:
            PredictionResult: BTC走势预测结果
        """
        if not self.is_model_loaded():
            raise RuntimeError("BTC预测模型未加载，无法执行预测")
        
        if not self.validate_input_data(data):
            raise ValueError("BTC预测模型输入数据格式错误")
        
        start_time = datetime.now()
        
        try:
            # 预处理输入数据
            model_input = self._preprocess_input(data)
            
            # 执行ONNX模型推理
            input_name = self._onnx_session.get_inputs()[0].name
            output_name = self._onnx_session.get_outputs()[0].name
            
            model_output = self._onnx_session.run(
                [output_name],
                {input_name: model_input}
            )[0]
            
            # 后处理输出
            result = self._postprocess_output(model_output, data)
            
            # 更新性能统计
            inference_time = (datetime.now() - start_time).total_seconds() * 1000
            self._inference_count += 1
            self._total_inference_time += inference_time
            
            result.metadata['inference_time_ms'] = inference_time
            
            return result
            
        except Exception as e:
            logger.error(f"BTC预测失败: {e}")
            raise RuntimeError(f"BTC预测失败: {e}")
    
    def get_model_info(self) -> ModelInfo:
        """
        获取BTC预测模型信息
        
        Returns:
            ModelInfo: 模型详细信息
        """
        if not self.is_model_loaded():
            raise RuntimeError("模型未加载，无法获取模型信息")
        
        # 获取模型文件大小
        model_size = None
        try:
            model_size = Path(self.model_path).stat().st_size
        except OSError:
            pass
        
        # 计算平均推理时间
        avg_inference_time = 0.0
        if self._inference_count > 0:
            avg_inference_time = self._total_inference_time / self._inference_count
        
        # 构建性能指标
        performance_metrics = {
            'inference_count': self._inference_count,
            'total_inference_time_ms': self._total_inference_time,
            'avg_inference_time_ms': avg_inference_time,
            'sequence_length': self.sequence_length,
            'feature_count': self.feature_count,
            'output_classes': self.output_classes
        }
        
        # 从元数据获取额外信息
        description = self._model_metadata.get(
            'description', 
            'BTC价格走势预测模型 - 基于24个时间步的历史数据预测未来1小时走势'
        )
        
        return ModelInfo(
            name="BTCPredictorModel",
            version=self.model_version,
            model_path=self.model_path,
            model_type="onnx",
            created_at=self._model_metadata.get('created_at', datetime.now(timezone.utc).isoformat()),
            model_size=model_size,
            description=description,
            input_shape=[1, self.sequence_length, self.feature_count],
            output_shape=[1, self.output_classes],
            performance_metrics=performance_metrics
        )


class BTCPredictor:
    """
    BTC预测器高级接口
    
    整合BTCPredictorModel、数据获取、缓存和调度功能，
    提供完整的BTC预测解决方案。支持：
    - 每小时自动预测调度
    - 手动触发预测
    - 结果缓存管理
    - 数据质量验证
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化BTC预测器
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 时区设置
        self.timezone = pytz.timezone(config.get("system", {}).get("timezone", "Asia/Shanghai"))
        
        # 核心组件
        self.model: Optional[BTCPredictorModel] = None
        self.data_provider = None  # 延迟导入避免循环依赖
        self.cache_interface = None
        self.scheduler = None
        
        # 配置参数
        self.prediction_config = config.get("ai_models", {}).get("btc_predictor", {})
        
        # 状态标志
        self.is_initialized = False
        self.is_running = False
        
        # 统计信息
        self.prediction_stats = {
            "total_predictions": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "last_prediction_time": None,
            "last_prediction_result": None,
            "last_error": None
        }
        
        self.logger.info("BTC预测器初始化完成")
    
    async def initialize(self) -> bool:
        """
        初始化BTC预测器的所有组件
        
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        try:
            self.logger.info("开始初始化BTC预测器...")
            
            # 1. 初始化AI模型
            model_config = self.prediction_config.copy()
            self.model = BTCPredictorModel(model_config)
            
            if not self.model.load_model():
                raise RuntimeError("BTC预测模型加载失败")
            
            self.logger.info("BTC预测模型加载成功")
            
            # 2. 初始化数据提供器（延迟导入）
            from ..data.nautilus_data_provider import NautilusDataProvider
            self.data_provider = NautilusDataProvider(self.config)
            
            if not await self.data_provider.initialize():
                raise RuntimeError("数据提供器初始化失败")
            
            self.logger.info("数据提供器初始化成功")
            
            # 3. 初始化缓存接口
            from ..data.cache_factory import get_cache
            self.cache_interface = get_cache()
            
            if not self.cache_interface or not self.cache_interface.is_connected():
                self.logger.warning("缓存接口不可用，预测结果将无法缓存")
            else:
                self.logger.info("缓存接口初始化成功")
            
            # 4. 初始化调度器
            from .btc_predictor_scheduler import BTCPredictorScheduler
            self.scheduler = BTCPredictorScheduler(self.config)
            
            if not await self.scheduler.initialize():
                raise RuntimeError("预测调度器初始化失败")
            
            self.logger.info("预测调度器初始化成功")
            
            self.is_initialized = True
            self.logger.info("BTC预测器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"BTC预测器初始化失败: {e}")
            self.is_initialized = False
            return False
    
    async def start_scheduler(self) -> bool:
        """
        启动每小时预测调度器
        
        Returns:
            bool: 启动成功返回True，失败返回False
        """
        try:
            if not self.is_initialized:
                self.logger.error("BTC预测器未初始化，无法启动调度器")
                return False
            
            if self.is_running:
                self.logger.warning("调度器已在运行中")
                return True
            
            # 启动调度器
            if not await self.scheduler.start():
                raise RuntimeError("调度器启动失败")
            
            self.is_running = True
            self.logger.info("BTC预测调度器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动BTC预测调度器失败: {e}")
            return False
    
    async def stop_scheduler(self) -> bool:
        """
        停止每小时预测调度器
        
        Returns:
            bool: 停止成功返回True，失败返回False
        """
        try:
            if not self.is_running:
                self.logger.warning("调度器未在运行")
                return True
            
            # 停止调度器
            if not await self.scheduler.stop():
                raise RuntimeError("调度器停止失败")
            
            self.is_running = False
            self.logger.info("BTC预测调度器停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止BTC预测调度器失败: {e}")
            return False
    
    async def predict_now(self) -> Optional[Dict[str, Any]]:
        """
        立即执行一次BTC预测
        
        Returns:
            Optional[Dict[str, Any]]: 预测结果字典，失败返回None
        """
        try:
            if not self.is_initialized:
                self.logger.error("BTC预测器未初始化，无法执行预测")
                return None
            
            self.logger.info("执行立即BTC预测...")
            
            # 通过调度器执行立即预测
            result = await self.scheduler.execute_immediate_prediction()
            
            if result:
                # 更新统计信息
                self.prediction_stats["total_predictions"] += 1
                self.prediction_stats["successful_predictions"] += 1
                self.prediction_stats["last_prediction_time"] = datetime.now(self.timezone)
                self.prediction_stats["last_prediction_result"] = result
                self.prediction_stats["last_error"] = None
                
                self.logger.info("立即预测执行成功")
                return result
            else:
                # 更新失败统计
                self.prediction_stats["total_predictions"] += 1
                self.prediction_stats["failed_predictions"] += 1
                self.prediction_stats["last_error"] = "立即预测返回空结果"
                
                self.logger.error("立即预测执行失败")
                return None
                
        except Exception as e:
            # 更新失败统计
            self.prediction_stats["total_predictions"] += 1
            self.prediction_stats["failed_predictions"] += 1
            self.prediction_stats["last_error"] = str(e)
            
            self.logger.error(f"立即预测执行异常: {e}")
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取BTC预测器状态信息
        
        Returns:
            Dict[str, Any]: 状态信息字典
        """
        status = {
            "is_initialized": self.is_initialized,
            "is_running": self.is_running,
            "timezone": str(self.timezone),
            "model_loaded": self.model.is_model_loaded() if self.model else False,
            "data_provider_initialized": self.data_provider.is_initialized if self.data_provider else False,
            "cache_available": self.cache_interface.is_connected() if self.cache_interface else False,
        }
        
        # 添加统计信息
        status.update(self.prediction_stats)
        
        # 添加调度器状态
        if self.scheduler:
            scheduler_status = self.scheduler.get_status()
            status.update({
                "scheduler_prediction_count": scheduler_status.get("prediction_count", 0),
                "scheduler_success_count": scheduler_status.get("success_count", 0),
                "scheduler_error_count": scheduler_status.get("error_count", 0),
                "scheduler_success_rate": scheduler_status.get("success_rate", 0),
                "next_run_time": scheduler_status.get("next_run_time"),
            })
        
        return status
    
    def get_latest_prediction(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的BTC预测结果
        
        Returns:
            Optional[Dict[str, Any]]: 最新预测结果，无结果返回None
        """
        try:
            if not self.cache_interface or not self.cache_interface.is_connected():
                self.logger.warning("缓存接口不可用，无法获取最新预测")
                return None
            
            # 从Redis获取最新预测结果
            result = self.cache_interface.get("btc_pred:latest")
            if result:
                self.logger.info("成功获取最新BTC预测结果")
                return result
            else:
                self.logger.info("暂无BTC预测结果")
                return None
                
        except Exception as e:
            self.logger.error(f"获取最新预测结果失败: {e}")
            return None
    
    async def cleanup(self) -> None:
        """
        清理BTC预测器资源
        """
        try:
            self.logger.info("开始清理BTC预测器资源...")
            
            # 停止调度器
            if self.is_running:
                await self.stop_scheduler()
            
            # 清理调度器
            if self.scheduler:
                await self.scheduler.cleanup()
            
            # 清理数据提供器
            if self.data_provider:
                await self.data_provider.shutdown()
            
            # 卸载模型
            if self.model:
                self.model.unload_model()
            
            # 重置状态
            self.is_initialized = False
            self.is_running = False
            
            self.logger.info("BTC预测器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理BTC预测器资源失败: {e}")
    
    def __str__(self) -> str:
        """
        返回BTC预测器的字符串表示
        """
        status = "运行中" if self.is_running else "已停止" if self.is_initialized else "未初始化"
        return f"BTC预测器 - 状态: {status}, 时区: {self.timezone}"
    
    def __repr__(self) -> str:
        """
        返回BTC预测器的详细字符串表示
        """
        return f"BTCPredictor(initialized={self.is_initialized}, running={self.is_running}, timezone='{self.timezone}')"