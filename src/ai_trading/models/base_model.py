"""
基础AI模型实现

提供AIModelInterface的基础实现，支持ONNX和Pickle格式的模型加载。
包含通用的模型管理功能，可作为具体AI模型实现的基类。
"""

import os
import pickle
import json
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging

from .interfaces import AIModelInterface, PredictionResult, ModelInfo

# 尝试导入ONNX运行时，如果不可用则记录警告
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    ort = None

logger = logging.getLogger(__name__)


class BaseAIModel(AIModelInterface):
    """
    基础AI模型实现类
    
    提供ONNX和Pickle格式模型的通用加载和管理功能。
    子类只需要实现具体的预测逻辑和数据预处理。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化基础AI模型
        
        Args:
            config: 模型配置参数
        """
        super().__init__(config)
        
        # ONNX会话对象
        self._onnx_session: Optional[ort.InferenceSession] = None
        
        # Pickle模型对象
        self._pickle_model: Optional[Any] = None
        
        # 模型元数据
        self._model_metadata: Dict[str, Any] = {}
        
        # 性能统计
        self._inference_count = 0
        self._total_inference_time = 0.0
        
    def load_model(self, model_path: Optional[str] = None) -> bool:
        """
        加载AI模型文件
        
        Args:
            model_path: 模型文件路径，如果为None则使用配置中的路径
            
        Returns:
            bool: True表示加载成功，False表示加载失败
        """
        try:
            # 确定模型路径
            if model_path is not None:
                self.model_path = model_path
            
            if not self.model_path:
                raise ValueError("模型路径未指定")
            
            # 验证模型文件是否存在
            if not self._validate_model_path(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            # 检测模型类型
            if self.model_type == 'auto':
                self.model_type = self._detect_model_type(self.model_path)
            
            logger.info(f"开始加载{self.model_type}模型: {self.model_path}")
            
            # 根据模型类型加载
            if self.model_type == 'onnx':
                success = self._load_onnx_model()
            elif self.model_type == 'pickle':
                success = self._load_pickle_model()
            else:
                raise ValueError(f"不支持的模型类型: {self.model_type}")
            
            if success:
                self._is_loaded = True
                self._create_model_info()
                logger.info(f"模型加载成功: {self.model_path}")
                return True
            else:
                logger.error(f"模型加载失败: {self.model_path}")
                return False
                
        except Exception as e:
            logger.error(f"加载模型时发生错误: {e}")
            self._is_loaded = False
            return False
    
    def _load_onnx_model(self) -> bool:
        """
        加载ONNX格式模型
        
        Returns:
            bool: 加载是否成功
        """
        if not ONNX_AVAILABLE:
            raise RuntimeError("ONNX Runtime未安装，无法加载ONNX模型")
        
        try:
            # 创建ONNX推理会话
            self._onnx_session = ort.InferenceSession(
                self.model_path,
                providers=['CPUExecutionProvider']  # 默认使用CPU
            )
            
            # 获取模型输入输出信息
            self._model_metadata = {
                'input_names': [input.name for input in self._onnx_session.get_inputs()],
                'output_names': [output.name for output in self._onnx_session.get_outputs()],
                'input_shapes': [input.shape for input in self._onnx_session.get_inputs()],
                'output_shapes': [output.shape for output in self._onnx_session.get_outputs()],
            }
            
            logger.info(f"ONNX模型元数据: {self._model_metadata}")
            return True
            
        except Exception as e:
            logger.error(f"加载ONNX模型失败: {e}")
            return False
    
    def _load_pickle_model(self) -> bool:
        """
        加载Pickle格式模型
        
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(self.model_path, 'rb') as f:
                self._pickle_model = pickle.load(f)
            
            # 尝试获取模型属性信息
            self._model_metadata = {}
            if hasattr(self._pickle_model, '__dict__'):
                # 获取模型的基本属性
                for attr in ['feature_names_', 'n_features_', 'classes_']:
                    if hasattr(self._pickle_model, attr):
                        self._model_metadata[attr] = getattr(self._pickle_model, attr)
            
            logger.info(f"Pickle模型元数据: {self._model_metadata}")
            return True
            
        except Exception as e:
            logger.error(f"加载Pickle模型失败: {e}")
            return False
    
    def predict(self, data: Dict[str, Any]) -> PredictionResult:
        """
        执行模型预测（基础实现）
        
        子类应该重写此方法实现具体的预测逻辑
        
        Args:
            data: 输入数据
            
        Returns:
            PredictionResult: 预测结果
        """
        if not self.is_model_loaded():
            raise RuntimeError("模型未加载，无法执行预测")
        
        if not self.validate_input_data(data):
            raise ValueError("输入数据格式错误")
        
        start_time = datetime.now()
        
        try:
            # 这里是基础实现，子类应该重写
            # 返回一个默认的预测结果
            result = PredictionResult(
                direction="sideways",
                confidence=0.5,
                timestamp=datetime.now(timezone.utc).isoformat(),
                metadata={
                    'model_type': self.model_type,
                    'model_path': self.model_path,
                    'inference_time_ms': 0
                }
            )
            
            # 更新性能统计
            inference_time = (datetime.now() - start_time).total_seconds() * 1000
            self._inference_count += 1
            self._total_inference_time += inference_time
            
            result.metadata['inference_time_ms'] = inference_time
            
            return result
            
        except Exception as e:
            logger.error(f"模型预测失败: {e}")
            raise RuntimeError(f"模型预测失败: {e}")
    
    def get_model_info(self) -> ModelInfo:
        """
        获取模型基本信息
        
        Returns:
            ModelInfo: 模型信息对象
        """
        if not self.is_model_loaded():
            raise RuntimeError("模型未加载，无法获取模型信息")
        
        # 获取模型文件大小
        model_size = None
        try:
            model_size = os.path.getsize(self.model_path)
        except OSError:
            pass
        
        # 计算平均推理时间
        avg_inference_time = 0.0
        if self._inference_count > 0:
            avg_inference_time = self._total_inference_time / self._inference_count
        
        # 构建性能指标
        performance_metrics = {
            'inference_count': self._inference_count,
            'total_inference_time_ms': self._total_inference_time,
            'avg_inference_time_ms': avg_inference_time,
        }
        
        # 获取输入输出形状
        input_shape = None
        output_shape = None
        if 'input_shapes' in self._model_metadata and self._model_metadata['input_shapes']:
            input_shape = self._model_metadata['input_shapes'][0]
        if 'output_shapes' in self._model_metadata and self._model_metadata['output_shapes']:
            output_shape = self._model_metadata['output_shapes'][0]
        
        return ModelInfo(
            name=Path(self.model_path).stem,
            version=self.model_version,
            model_path=self.model_path,
            model_type=self.model_type,
            created_at=datetime.now(timezone.utc).isoformat(),
            model_size=model_size,
            description=f"基础AI模型 - {self.model_type}格式",
            input_shape=input_shape,
            output_shape=output_shape,
            performance_metrics=performance_metrics
        )
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        验证输入数据格式
        
        Args:
            data: 待验证的输入数据
            
        Returns:
            bool: True表示数据格式正确，False表示格式错误
        """
        # 调用父类的基础验证
        if not super().validate_input_data(data):
            return False
        
        # 子类可以重写此方法实现更具体的验证逻辑
        return True
    
    def _create_model_info(self) -> None:
        """
        创建模型信息对象
        """
        try:
            self._model_info = self.get_model_info()
        except Exception as e:
            logger.warning(f"创建模型信息失败: {e}")
            self._model_info = None
    
    def get_inference_stats(self) -> Dict[str, Any]:
        """
        获取推理性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计信息
        """
        avg_time = 0.0
        if self._inference_count > 0:
            avg_time = self._total_inference_time / self._inference_count
        
        return {
            'inference_count': self._inference_count,
            'total_inference_time_ms': self._total_inference_time,
            'avg_inference_time_ms': avg_time,
            'model_loaded': self._is_loaded,
            'model_type': self.model_type,
            'model_path': self.model_path
        }
    
    def reset_stats(self) -> None:
        """
        重置性能统计信息
        """
        self._inference_count = 0
        self._total_inference_time = 0.0
        logger.info("性能统计信息已重置")


