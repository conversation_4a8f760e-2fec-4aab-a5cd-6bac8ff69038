"""
AI模型接口定义模块

本模块定义了AI模型的抽象接口和相关数据结构，为BTC预测AI和交易决策AI提供统一的接口规范。
支持ONNX和Pickle格式的模型加载，提供模型信息查询和预测功能。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
import json
from pathlib import Path


@dataclass
class PredictionResult:
    """
    AI模型预测结果数据类
    
    用于封装AI模型的预测输出，包含预测方向、置信度、时间戳等信息。
    支持序列化为字典格式，便于存储到Redis缓存中。
    """
    
    # 预测方向：bullish(看涨), bearish(看跌), sideways(震荡)
    direction: str
    
    # 置信度：0.0-1.0之间的浮点数，表示预测的可信程度
    confidence: float
    
    # 预测生成时间戳：ISO 8601格式，包含时区信息
    timestamp: str
    
    # 预测有效期：ISO 8601格式，超过此时间预测失效
    valid_until: Optional[str] = None
    
    # 额外的元数据信息：模型版本、输入特征等
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """
        数据验证和初始化后处理
        """
        # 验证direction参数
        valid_directions = ["bullish", "bearish", "sideways"]
        if self.direction not in valid_directions:
            raise ValueError(f"direction必须是{valid_directions}中的一个，当前值：{self.direction}")
        
        # 验证confidence参数
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError(f"confidence必须在0.0-1.0之间，当前值：{self.confidence}")
        
        # 初始化metadata
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于JSON序列化和Redis存储
        
        Returns:
            Dict[str, Any]: 包含所有字段的字典
        """
        return asdict(self)
    
    def to_json(self) -> str:
        """
        转换为JSON字符串格式
        
        Returns:
            str: JSON格式的字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PredictionResult':
        """
        从字典创建PredictionResult实例
        
        Args:
            data: 包含预测结果数据的字典
            
        Returns:
            PredictionResult: 创建的实例
        """
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'PredictionResult':
        """
        从JSON字符串创建PredictionResult实例
        
        Args:
            json_str: JSON格式的字符串
            
        Returns:
            PredictionResult: 创建的实例
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def is_valid(self, current_time: Optional[datetime] = None) -> bool:
        """
        检查预测结果是否仍然有效
        
        Args:
            current_time: 当前时间，如果为None则使用系统当前时间
            
        Returns:
            bool: True表示有效，False表示已过期
        """
        if self.valid_until is None:
            return True
        
        if current_time is None:
            current_time = datetime.now()
        
        try:
            valid_until_dt = datetime.fromisoformat(self.valid_until.replace('Z', '+00:00'))
            return current_time < valid_until_dt
        except (ValueError, AttributeError):
            # 如果时间格式解析失败，认为预测无效
            return False


@dataclass
class ModelInfo:
    """
    AI模型信息数据类
    
    用于描述AI模型的基本信息，包括版本、路径、性能指标等。
    """
    
    # 模型名称
    name: str
    
    # 模型版本号
    version: str
    
    # 模型文件路径
    model_path: str
    
    # 模型类型：onnx, pickle等
    model_type: str
    
    # 模型创建时间
    created_at: str
    
    # 模型大小（字节）
    model_size: Optional[int] = None
    
    # 模型描述
    description: Optional[str] = None
    
    # 输入特征维度
    input_shape: Optional[List[int]] = None
    
    # 输出维度
    output_shape: Optional[List[int]] = None
    
    # 性能指标
    performance_metrics: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        """
        初始化后处理
        """
        if self.performance_metrics is None:
            self.performance_metrics = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 包含所有字段的字典
        """
        return asdict(self)


class AIModelInterface(ABC):
    """
    AI模型抽象接口基类
    
    定义了所有AI模型必须实现的基本接口，包括模型加载、预测、信息获取等功能。
    支持ONNX和Pickle格式的模型文件，提供统一的模型管理接口。
    
    子类需要实现：
    - load_model(): 加载模型文件
    - predict(): 执行预测
    - get_model_info(): 获取模型信息
    - is_model_loaded(): 检查模型是否已加载
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化AI模型接口
        
        Args:
            config: 模型配置参数，包含模型路径、版本等信息
        """
        self.config = config or {}
        self._model = None
        self._model_info: Optional[ModelInfo] = None
        self._is_loaded = False
        
        # 从配置中获取模型路径
        self.model_path = self.config.get('model_path', '')
        self.model_version = self.config.get('version', 'unknown')
        self.model_type = self.config.get('model_type', 'auto')  # auto, onnx, pickle
    
    @abstractmethod
    def load_model(self, model_path: Optional[str] = None) -> bool:
        """
        加载AI模型文件
        
        Args:
            model_path: 模型文件路径，如果为None则使用配置中的路径
            
        Returns:
            bool: True表示加载成功，False表示加载失败
            
        Raises:
            FileNotFoundError: 模型文件不存在
            ValueError: 模型格式不支持或文件损坏
        """
        pass
    
    @abstractmethod
    def predict(self, data: Dict[str, Any]) -> PredictionResult:
        """
        执行模型预测
        
        Args:
            data: 输入数据，格式取决于具体的模型类型
                  对于BTC预测模型：包含OHLCV历史数据
                  对于交易决策模型：包含币种数据和BTC预测结果
                  
        Returns:
            PredictionResult: 预测结果对象
            
        Raises:
            RuntimeError: 模型未加载或预测失败
            ValueError: 输入数据格式错误
        """
        pass
    
    @abstractmethod
    def get_model_info(self) -> ModelInfo:
        """
        获取模型基本信息
        
        Returns:
            ModelInfo: 模型信息对象，包含版本、路径、性能指标等
            
        Raises:
            RuntimeError: 模型未加载
        """
        pass
    
    def is_model_loaded(self) -> bool:
        """
        检查模型是否已成功加载
        
        Returns:
            bool: True表示已加载，False表示未加载
        """
        return self._is_loaded
    
    def unload_model(self) -> bool:
        """
        卸载当前模型，释放内存资源
        
        Returns:
            bool: True表示卸载成功，False表示卸载失败
        """
        try:
            self._model = None
            self._model_info = None
            self._is_loaded = False
            return True
        except Exception as e:
            print(f"模型卸载失败: {e}")
            return False
    
    def reload_model(self, model_path: Optional[str] = None) -> bool:
        """
        重新加载模型，用于模型热更新
        
        Args:
            model_path: 新的模型文件路径，如果为None则使用当前路径
            
        Returns:
            bool: True表示重载成功，False表示重载失败
        """
        # 先卸载当前模型
        self.unload_model()
        
        # 重新加载模型
        return self.load_model(model_path)
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        验证输入数据格式是否正确
        
        Args:
            data: 待验证的输入数据
            
        Returns:
            bool: True表示数据格式正确，False表示格式错误
        """
        # 基础验证：检查数据是否为字典类型
        if not isinstance(data, dict):
            return False
        
        # 子类可以重写此方法实现更具体的验证逻辑
        return True
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的模型文件格式列表
        
        Returns:
            List[str]: 支持的格式列表，如['onnx', 'pickle']
        """
        return ['onnx', 'pickle']
    
    def _detect_model_type(self, model_path: str) -> str:
        """
        根据文件扩展名自动检测模型类型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            str: 检测到的模型类型
        """
        path = Path(model_path)
        suffix = path.suffix.lower()
        
        if suffix == '.onnx':
            return 'onnx'
        elif suffix in ['.pkl', '.pickle']:
            return 'pickle'
        else:
            return 'unknown'
    
    def _validate_model_path(self, model_path: str) -> bool:
        """
        验证模型文件路径是否有效
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            bool: True表示路径有效，False表示路径无效
        """
        if not model_path:
            return False
        
        path = Path(model_path)
        return path.exists() and path.is_file()
    
    def __str__(self) -> str:
        """
        返回模型的字符串表示
        """
        status = "已加载" if self._is_loaded else "未加载"
        return f"AI模型接口 - 路径: {self.model_path}, 版本: {self.model_version}, 状态: {status}"
    
    def __repr__(self) -> str:
        """
        返回模型的详细字符串表示
        """
        return f"AIModelInterface(model_path='{self.model_path}', version='{self.model_version}', loaded={self._is_loaded})"