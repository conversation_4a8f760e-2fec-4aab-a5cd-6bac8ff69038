"""
BTC预测AI调度器

实现每小时BTC预测调度功能，严格按照Asia/Shanghai时区执行。
负责定时触发BTC预测、数据获取、结果缓存等功能。
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Callable
import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from .btc_predictor import BTCPredictorModel
from ..data.nautilus_data_provider import NautilusDataProvider
from ..data.cache_interface import CacheInterface
from ..data.cache_factory import get_cache

logger = logging.getLogger(__name__)


class BTCPredictorScheduler:
    """
    BTC预测AI调度器
    
    负责按照Asia/Shanghai时区每小时整点执行BTC预测，
    获取历史数据，执行AI推理，并将结果存储到Redis缓存。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化BTC预测调度器
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 时区设置 - 严格使用Asia/Shanghai
        self.timezone = pytz.timezone(config.get("system", {}).get("timezone", "Asia/Shanghai"))
        
        # BTC预测模型
        self.btc_predictor: Optional[BTCPredictorModel] = None
        
        # 数据提供器
        self.data_provider: Optional[NautilusDataProvider] = None
        
        # 缓存接口
        self.cache_interface: Optional[CacheInterface] = None
        
        # 调度器
        self.scheduler: Optional[AsyncIOScheduler] = None
        
        # 预测配置
        self.prediction_config = config.get("ai_models", {}).get("btc_predictor", {})
        self.update_interval = self.prediction_config.get("update_interval", 3600)  # 1小时
        self.prediction_horizon = self.prediction_config.get("prediction_horizon", 3600)  # 1小时
        
        # 状态标志
        self.is_running = False
        self.is_initialized = False
        
        # 统计信息
        self.prediction_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_prediction_time: Optional[datetime] = None
        self.last_error: Optional[str] = None
        
        # 回调函数
        self.on_prediction_success: Optional[Callable] = None
        self.on_prediction_error: Optional[Callable] = None
        
        self.logger.info("BTC预测调度器初始化完成")
    
    async def initialize(self) -> bool:
        """
        初始化调度器组件
        
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        try:
            self.logger.info("开始初始化BTC预测调度器...")
            
            # 1. 初始化BTC预测模型
            model_config = self.prediction_config.copy()
            self.btc_predictor = BTCPredictorModel(model_config)
            
            # 加载模型
            model_loaded = self.btc_predictor.load_model()
            if not model_loaded:
                raise RuntimeError("BTC预测模型加载失败")
            
            self.logger.info("BTC预测模型加载成功")
            
            # 2. 初始化数据提供器
            self.data_provider = NautilusDataProvider(self.config)
            data_initialized = await self.data_provider.initialize()
            if not data_initialized:
                raise RuntimeError("Nautilus数据提供器初始化失败")
            
            self.logger.info("Nautilus数据提供器初始化成功")
            
            # 3. 初始化缓存接口
            self.cache_interface = get_cache()
            if not self.cache_interface or not self.cache_interface.is_connected():
                self.logger.warning("缓存接口不可用，预测结果将无法缓存")
            else:
                self.logger.info("缓存接口初始化成功")
            
            # 4. 初始化调度器
            self.scheduler = AsyncIOScheduler(timezone=self.timezone)
            
            # 添加每小时整点执行的任务
            self.scheduler.add_job(
                func=self._execute_prediction,
                trigger=CronTrigger(
                    minute=0,  # 每小时的0分钟执行
                    timezone=self.timezone
                ),
                id="btc_prediction_hourly",
                name="BTC预测每小时任务",
                max_instances=1,  # 防止重复执行
                coalesce=True,    # 合并错过的任务
                misfire_grace_time=300  # 5分钟的容错时间
            )
            
            self.logger.info("调度器配置完成")
            
            self.is_initialized = True
            self.logger.info("BTC预测调度器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"BTC预测调度器初始化失败: {e}")
            self.is_initialized = False
            return False
    
    async def start(self) -> bool:
        """
        启动BTC预测调度器
        
        Returns:
            bool: 启动成功返回True，失败返回False
        """
        try:
            if not self.is_initialized:
                self.logger.error("调度器未初始化，无法启动")
                return False
            
            if self.is_running:
                self.logger.warning("调度器已在运行中")
                return True
            
            self.logger.info("启动BTC预测调度器...")
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            # 记录下次执行时间
            next_run_time = self._get_next_prediction_time()
            self.logger.info(f"BTC预测调度器启动成功，下次执行时间: {next_run_time}")
            
            # 可选：立即执行一次预测（用于测试）
            if self.config.get("system", {}).get("immediate_prediction", False):
                self.logger.info("执行立即预测...")
                await self._execute_prediction()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动BTC预测调度器失败: {e}")
            self.is_running = False
            return False
    
    async def stop(self) -> bool:
        """
        停止BTC预测调度器
        
        Returns:
            bool: 停止成功返回True，失败返回False
        """
        try:
            if not self.is_running:
                self.logger.warning("调度器未在运行")
                return True
            
            self.logger.info("停止BTC预测调度器...")
            
            # 停止调度器
            if self.scheduler:
                self.scheduler.shutdown(wait=True)
            
            self.is_running = False
            self.logger.info("BTC预测调度器已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止BTC预测调度器失败: {e}")
            return False
    
    async def _execute_prediction(self) -> None:
        """
        执行BTC预测的核心逻辑
        
        这是调度器的核心方法，负责：
        1. 获取BTC历史数据
        2. 执行AI预测
        3. 存储预测结果到Redis
        4. 更新统计信息
        """
        prediction_start_time = datetime.now(self.timezone)
        self.prediction_count += 1
        
        try:
            self.logger.info(f"开始执行BTC预测 (第{self.prediction_count}次)")
            
            # 1. 获取BTC历史数据 - 严格使用Nautilus DataEngine
            btc_bars = await self.data_provider.get_historical_bars(
                symbol="BTC/USDT",
                timeframe="1-MINUTE",
                count=24 * 60,  # 24小时的分钟数据
                use_cache=True
            )
            
            if not btc_bars:
                raise RuntimeError("无法获取BTC历史数据")
            
            self.logger.info(f"成功获取BTC历史数据: {len(btc_bars)}条")
            
            # 2. 验证数据质量
            validation_result = self.data_provider.validate_data_quality(btc_bars, "BTC/USDT")
            if not validation_result.is_valid:
                self.logger.warning(f"BTC数据质量检查失败: {validation_result.errors}")
                # 如果数据质量不佳但不是完全无效，继续执行但记录警告
                if validation_result.data_quality_score < 0.5:
                    raise RuntimeError(f"BTC数据质量过低: {validation_result.data_quality_score}")
            
            # 3. 预处理数据
            processed_data = self.data_provider.preprocess_market_data(btc_bars, "BTC/USDT")
            if not processed_data:
                raise RuntimeError("BTC数据预处理失败")
            
            # 4. 准备AI模型输入数据
            model_input = self._prepare_model_input(processed_data)
            
            # 5. 执行AI预测
            prediction_result = self.btc_predictor.predict(model_input)
            
            self.logger.info(f"BTC预测完成: {prediction_result.direction} (置信度: {prediction_result.confidence:.3f})")
            
            # 6. 存储预测结果到Redis
            if self.cache_interface and self.cache_interface.is_connected():
                cache_success = await self._store_prediction_result(prediction_result)
                if cache_success:
                    self.logger.info("BTC预测结果已存储到缓存")
                else:
                    self.logger.warning("BTC预测结果缓存失败")
            
            # 7. 更新统计信息
            self.success_count += 1
            self.last_prediction_time = prediction_start_time
            self.last_error = None
            
            # 8. 调用成功回调
            if self.on_prediction_success:
                try:
                    await self.on_prediction_success(prediction_result)
                except Exception as callback_error:
                    self.logger.warning(f"预测成功回调执行失败: {callback_error}")
            
            execution_time = (datetime.now(self.timezone) - prediction_start_time).total_seconds()
            self.logger.info(f"BTC预测执行完成，耗时: {execution_time:.2f}秒")
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            
            self.logger.error(f"BTC预测执行失败: {e}")
            
            # 存储错误状态到Redis
            if self.cache_interface and self.cache_interface.is_connected():
                await self._store_error_status(str(e))
            
            # 调用错误回调
            if self.on_prediction_error:
                try:
                    await self.on_prediction_error(e)
                except Exception as callback_error:
                    self.logger.warning(f"预测错误回调执行失败: {callback_error}")
    
    def _prepare_model_input(self, processed_data) -> Dict[str, Any]:
        """
        准备AI模型输入数据
        
        Args:
            processed_data: 预处理后的市场数据
            
        Returns:
            Dict[str, Any]: AI模型输入数据
        """
        try:
            # 获取最近24个时间步的数据
            ohlcv_array = processed_data.ohlcv_array
            features = processed_data.features
            
            # 确保有足够的数据
            if len(ohlcv_array) < 24:
                raise ValueError(f"数据不足，需要24个时间步，实际: {len(ohlcv_array)}")
            
            # 取最近24个时间步
            recent_ohlcv = ohlcv_array[-24:, 1:6]  # 排除时间戳，只取OHLCV
            
            # 准备技术指标数据 (73个特征)
            tech_indicators = []
            feature_names = [
                'sma_5', 'sma_10', 'sma_20', 'sma_50',
                'ema_12', 'ema_26', 'macd', 'macd_signal', 'macd_histogram',
                'rsi', 'bb_upper', 'bb_lower', 'bb_middle', 'atr',
                'volume_sma', 'volume_ratio', 'price_change', 'price_change_pct'
            ]
            
            # 构建技术指标矩阵
            tech_matrix = []
            for i in range(24):  # 24个时间步
                step_features = []
                for feature_name in feature_names:
                    if feature_name in features:
                        feature_values = features[feature_name]
                        if len(feature_values) > i:
                            step_features.append(feature_values[-(24-i)])
                        else:
                            step_features.append(0.0)  # 填充默认值
                    else:
                        step_features.append(0.0)  # 填充默认值
                
                # 如果特征数不足73，用0填充
                while len(step_features) < 73:
                    step_features.append(0.0)
                
                # 如果特征数超过73，截断
                step_features = step_features[:73]
                tech_matrix.append(step_features)
            
            tech_indicators = tech_matrix
            
            model_input = {
                'ohlcv_data': recent_ohlcv.tolist(),
                'technical_indicators': tech_indicators,
                'timestamp': datetime.now(self.timezone).isoformat()
            }
            
            self.logger.debug(f"模型输入数据准备完成: OHLCV形状{recent_ohlcv.shape}, 技术指标形状{len(tech_indicators)}x{len(tech_indicators[0])}")
            return model_input
            
        except Exception as e:
            self.logger.error(f"准备模型输入数据失败: {e}")
            raise
    
    async def _store_prediction_result(self, prediction_result) -> bool:
        """
        存储预测结果到Redis缓存
        
        Args:
            prediction_result: BTC预测结果
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        try:
            # 存储到Redis的键名：btc_pred:latest
            cache_key = "btc_pred:latest"
            
            # 设置TTL为65分钟（比1小时稍长，确保下次预测前不会过期）
            ttl = 65 * 60  # 65分钟
            
            # 存储预测结果
            success = self.cache_interface.set(cache_key, prediction_result.to_dict(), ttl)
            
            if success:
                self.logger.info(f"BTC预测结果已存储: {cache_key}, TTL: {ttl}秒")
                
                # 同时存储状态信息
                status_key = "btc_pred:status"
                status_data = {
                    "status": "available",
                    "last_update": prediction_result.timestamp,
                    "next_update": prediction_result.valid_until,
                    "prediction_count": self.prediction_count,
                    "success_rate": self.success_count / self.prediction_count if self.prediction_count > 0 else 0
                }
                self.cache_interface.set(status_key, status_data, ttl)
                
                return True
            else:
                self.logger.error("BTC预测结果存储失败")
                return False
                
        except Exception as e:
            self.logger.error(f"存储BTC预测结果异常: {e}")
            return False
    
    async def _store_error_status(self, error_message: str) -> bool:
        """
        存储错误状态到Redis
        
        Args:
            error_message: 错误信息
            
        Returns:
            bool: 存储成功返回True，失败返回False
        """
        try:
            status_key = "btc_pred:status"
            status_data = {
                "status": "unavailable",
                "error": error_message,
                "last_error_time": datetime.now(self.timezone).isoformat(),
                "prediction_count": self.prediction_count,
                "error_count": self.error_count,
                "success_rate": self.success_count / self.prediction_count if self.prediction_count > 0 else 0
            }
            
            # 设置较短的TTL，错误状态不需要保持太久
            ttl = 10 * 60  # 10分钟
            
            success = self.cache_interface.set(status_key, status_data, ttl)
            if success:
                self.logger.info("BTC预测错误状态已存储")
            
            return success
            
        except Exception as e:
            self.logger.error(f"存储BTC预测错误状态异常: {e}")
            return False
    
    def _get_next_prediction_time(self) -> datetime:
        """
        获取下次预测执行时间
        
        Returns:
            datetime: 下次执行时间
        """
        now = datetime.now(self.timezone)
        # 下个整点时间
        next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        return next_hour
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取调度器状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        next_run_time = None
        if self.is_running and self.scheduler:
            job = self.scheduler.get_job("btc_prediction_hourly")
            if job:
                next_run_time = job.next_run_time.isoformat() if job.next_run_time else None
        
        return {
            "is_initialized": self.is_initialized,
            "is_running": self.is_running,
            "prediction_count": self.prediction_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / self.prediction_count if self.prediction_count > 0 else 0,
            "last_prediction_time": self.last_prediction_time.isoformat() if self.last_prediction_time else None,
            "last_error": self.last_error,
            "next_run_time": next_run_time,
            "timezone": str(self.timezone),
            "update_interval": self.update_interval,
            "model_loaded": self.btc_predictor.is_model_loaded() if self.btc_predictor else False,
            "data_provider_initialized": self.data_provider.is_initialized if self.data_provider else False,
            "cache_available": self.cache_interface.is_connected() if self.cache_interface else False
        }
    
    async def execute_immediate_prediction(self) -> Optional[Dict[str, Any]]:
        """
        立即执行一次BTC预测（用于测试和手动触发）
        
        Returns:
            Optional[Dict[str, Any]]: 预测结果，失败返回None
        """
        try:
            self.logger.info("执行立即BTC预测...")
            await self._execute_prediction()
            
            # 从缓存获取最新预测结果
            if self.cache_interface and self.cache_interface.is_connected():
                result = self.cache_interface.get("btc_pred:latest")
                if result:
                    self.logger.info("立即预测执行成功")
                    return result
            
            self.logger.warning("立即预测执行完成，但无法获取结果")
            return None
            
        except Exception as e:
            self.logger.error(f"立即预测执行失败: {e}")
            return None
    
    def set_callbacks(self, 
                     on_success: Optional[Callable] = None, 
                     on_error: Optional[Callable] = None) -> None:
        """
        设置预测结果回调函数
        
        Args:
            on_success: 预测成功回调函数
            on_error: 预测失败回调函数
        """
        self.on_prediction_success = on_success
        self.on_prediction_error = on_error
        self.logger.info("预测回调函数已设置")
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            self.logger.info("开始清理BTC预测调度器资源...")
            
            # 停止调度器
            await self.stop()
            
            # 清理数据提供器
            if self.data_provider:
                await self.data_provider.shutdown()
            
            # 清理模型
            if self.btc_predictor:
                self.btc_predictor.unload_model()
            
            self.logger.info("BTC预测调度器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理BTC预测调度器资源失败: {e}")