"""
交易决策AI模块实现

基于真实的ONNX模型文件实现交易决策功能。
集成BTC预测结果作为辅助输入，实现信号增强算法。
支持实时推理，延迟要求≤100ms主流币种。

模型输入：币种OHLCV数据 + 技术指标 + BTC预测信号
模型输出：交易信号 (买入/卖出/持有) + 置信度

主要组件：
- TradingAIModel: 核心交易决策AI模型实现
- TradingAI: 高级接口，整合数据获取、缓存和信号增强
"""

import numpy as np
import json
import asyncio
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import logging
import pytz
import time

from .base_model import BaseAIModel
from .interfaces import PredictionResult, ModelInfo

logger = logging.getLogger(__name__)


class TradingAIModel(BaseAIModel):
    """
    交易决策AI模型实现类
    
    使用真实的ONNX模型文件进行交易决策。
    支持多种输入特征，包括OHLCV数据、技术指标和BTC预测信号。
    实现信号增强算法，提高决策准确性。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化交易决策AI模型
        
        Args:
            config: 模型配置参数
        """
        super().__init__(config)
        
        # 交易决策模型特定配置
        self.sequence_length = 24  # 输入序列长度（24个时间步）
        self.ohlcv_features = 5    # OHLCV特征数量
        self.tech_features = 29    # 技术指标特征数量（调整为29以匹配模型期望的37总特征）
        self.btc_features = 3      # BTC预测特征数量（方向概率分布）
        self.total_features = self.ohlcv_features + self.tech_features + self.btc_features  # 总特征数 = 37
        self.output_classes = 3    # 输出类别数：[卖出, 持有, 买入]
        
        # 类别映射
        self.class_mapping = {
            0: "SELL",    # 卖出
            1: "HOLD",    # 持有
            2: "BUY"      # 买入
        }
        
        # 方向映射（用于与BTC预测结果对应）
        self.direction_mapping = {
            "SELL": "bearish",
            "HOLD": "sideways", 
            "BUY": "bullish"
        }
        
        # 默认模型路径
        if not self.model_path:
            self.model_path = "models/trading_ai/trading_ai.onnx"
        
        # 性能要求配置
        self.inference_timeout_ms = config.get("ai_models", {}).get("trading_ai", {}).get("inference_timeout", 100)
        
        # 信号增强配置
        self.btc_weight = 0.3  # BTC信号权重
        
        # 加载模型信息文件
        self._load_model_metadata()
    
    def _load_model_metadata(self) -> None:
        """
        加载模型元数据信息
        """
        try:
            model_dir = Path(self.model_path).parent
            info_file = model_dir / "model_info.json"
            
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    self._model_metadata.update(metadata)
                    logger.info(f"加载交易AI模型元数据: {info_file}")
            else:
                logger.warning(f"交易AI模型信息文件不存在: {info_file}")
                
        except Exception as e:
            logger.warning(f"加载交易AI模型元数据失败: {e}")
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        验证交易决策AI模型的输入数据格式
        
        Args:
            data: 输入数据，应包含币种OHLCV数据、技术指标和BTC预测信号
            
        Returns:
            bool: True表示数据格式正确，False表示格式错误
        """
        # 调用父类基础验证
        if not super().validate_input_data(data):
            return False
        
        # 检查必需的字段
        required_fields = ['ohlcv_data', 'technical_indicators', 'symbol']
        for field in required_fields:
            if field not in data:
                logger.error(f"缺少必需字段: {field}")
                return False
        
        # 验证OHLCV数据格式
        ohlcv_data = data['ohlcv_data']
        if not isinstance(ohlcv_data, (list, np.ndarray)):
            logger.error("ohlcv_data必须是列表或numpy数组")
            return False
        
        # 转换为numpy数组进行形状检查
        try:
            ohlcv_array = np.array(ohlcv_data)
            if ohlcv_array.shape != (self.sequence_length, self.ohlcv_features):
                logger.error(f"ohlcv_data形状错误，期望({self.sequence_length}, {self.ohlcv_features})，实际{ohlcv_array.shape}")
                return False
        except Exception as e:
            logger.error(f"ohlcv_data格式错误: {e}")
            return False
        
        # 验证技术指标数据
        tech_indicators = data['technical_indicators']
        if not isinstance(tech_indicators, (list, np.ndarray)):
            logger.error("technical_indicators必须是列表或numpy数组")
            return False
        
        try:
            tech_array = np.array(tech_indicators)
            if tech_array.shape != (self.sequence_length, self.tech_features):
                logger.error(f"technical_indicators形状错误，期望({self.sequence_length}, {self.tech_features})，实际{tech_array.shape}")
                return False
        except Exception as e:
            logger.error(f"technical_indicators格式错误: {e}")
            return False
        
        # 验证交易对符号
        symbol = data['symbol']
        if not isinstance(symbol, str) or not symbol:
            logger.error("symbol必须是非空字符串")
            return False
        
        # BTC预测信号是可选的，如果存在则验证格式
        if 'btc_signal' in data:
            btc_signal = data['btc_signal']
            if btc_signal is not None:
                if not isinstance(btc_signal, dict):
                    logger.error("btc_signal必须是字典类型")
                    return False
                
                required_btc_fields = ['direction', 'confidence']
                for field in required_btc_fields:
                    if field not in btc_signal:
                        logger.error(f"btc_signal缺少必需字段: {field}")
                        return False
        
        return True
    
    def _preprocess_input(self, data: Dict[str, Any]) -> np.ndarray:
        """
        预处理输入数据为模型所需格式
        
        Args:
            data: 原始输入数据
            
        Returns:
            np.ndarray: 预处理后的输入数据，形状为(1, 24, total_features)
        """
        # 获取OHLCV数据
        ohlcv_data = np.array(data['ohlcv_data'], dtype=np.float32)
        
        # 获取技术指标数据
        tech_indicators = np.array(data['technical_indicators'], dtype=np.float32)
        
        # 处理BTC预测信号
        btc_signal = data.get('btc_signal')
        if btc_signal is not None and isinstance(btc_signal, dict):
            # 从BTC预测结果中提取概率分布
            if 'metadata' in btc_signal and 'probabilities' in btc_signal['metadata']:
                probabilities = btc_signal['metadata']['probabilities']
                btc_features = np.array([
                    probabilities.get('bearish', 0.33),
                    probabilities.get('sideways', 0.33),
                    probabilities.get('bullish', 0.33)
                ], dtype=np.float32)
            else:
                # 如果没有概率分布，根据方向和置信度构造
                direction = btc_signal.get('direction', 'sideways')
                confidence = btc_signal.get('confidence', 0.5)
                
                if direction == 'bearish':
                    btc_features = np.array([confidence, (1-confidence)/2, (1-confidence)/2], dtype=np.float32)
                elif direction == 'bullish':
                    btc_features = np.array([(1-confidence)/2, (1-confidence)/2, confidence], dtype=np.float32)
                else:  # sideways
                    btc_features = np.array([(1-confidence)/2, confidence, (1-confidence)/2], dtype=np.float32)
        else:
            # 如果没有BTC信号，使用中性值
            btc_features = np.array([0.33, 0.34, 0.33], dtype=np.float32)
        
        # 将BTC特征扩展到所有时间步
        btc_features_expanded = np.tile(btc_features, (self.sequence_length, 1))
        
        # 合并所有特征
        combined_features = np.concatenate([ohlcv_data, tech_indicators, btc_features_expanded], axis=1)
        
        # 添加batch维度
        model_input = np.expand_dims(combined_features, axis=0)
        
        logger.debug(f"预处理后的输入形状: {model_input.shape}")
        return model_input
    
    def _postprocess_output(self, model_output: np.ndarray, input_data: Dict[str, Any]) -> PredictionResult:
        """
        后处理模型输出为交易决策结果
        
        Args:
            model_output: 模型原始输出，形状为(1, 3)
            input_data: 原始输入数据，用于元数据
            
        Returns:
            PredictionResult: 格式化的交易决策结果
        """
        # 获取概率分布
        probabilities = model_output[0]  # 移除batch维度
        
        # 应用softmax确保概率和为1
        exp_probs = np.exp(probabilities - np.max(probabilities))
        softmax_probs = exp_probs / np.sum(exp_probs)
        
        # 获取最高概率的类别
        predicted_class = np.argmax(softmax_probs)
        base_confidence = float(softmax_probs[predicted_class])
        action = self.class_mapping[predicted_class]
        
        # 应用信号增强算法
        enhanced_confidence = self._apply_signal_enhancement(
            action, base_confidence, input_data.get('btc_signal')
        )
        
        # 创建交易决策结果
        current_time = datetime.now(timezone.utc)
        
        # 计算有效期（交易信号通常有效期较短，设为5分钟）
        valid_until = datetime.fromtimestamp(
            current_time.timestamp() + 300,  # 5分钟后
            tz=timezone.utc
        ).isoformat()
        
        result = PredictionResult(
            direction=self.direction_mapping[action],  # 转换为标准方向格式
            confidence=enhanced_confidence,
            timestamp=current_time.isoformat(),
            valid_until=valid_until,
            metadata={
                'model_type': 'trading_ai',
                'model_path': self.model_path,
                'model_version': self.model_version,
                'symbol': input_data.get('symbol', 'UNKNOWN'),
                'action': action,
                'base_confidence': base_confidence,
                'enhanced_confidence': enhanced_confidence,
                'btc_enhancement_applied': input_data.get('btc_signal') is not None,
                'probabilities': {
                    'sell': float(softmax_probs[0]),
                    'hold': float(softmax_probs[1]),
                    'buy': float(softmax_probs[2])
                },
                'input_sequence_length': self.sequence_length,
                'feature_count': self.total_features,
                'prediction_horizon': '5m'
            }
        )
        
        logger.info(f"交易决策结果 {input_data.get('symbol', 'UNKNOWN')}: {action} (基础置信度: {base_confidence:.3f}, 增强置信度: {enhanced_confidence:.3f})")
        return result
    
    def _apply_signal_enhancement(self, action: str, base_confidence: float, btc_signal: Optional[Dict[str, Any]]) -> float:
        """
        应用信号增强算法
        
        根据BTC预测结果增强或减弱交易信号的置信度。
        公式：final_confidence = coin_confidence * (1 + 0.3 * btc_confidence) 当方向一致时
        
        Args:
            action: 交易动作 (BUY/SELL/HOLD)
            base_confidence: 基础置信度
            btc_signal: BTC预测信号
            
        Returns:
            float: 增强后的置信度
        """
        if btc_signal is None:
            logger.debug("无BTC信号，使用基础置信度")
            return base_confidence
        
        try:
            btc_direction = btc_signal.get('direction', 'sideways')
            btc_confidence = btc_signal.get('confidence', 0.5)
            
            # 获取币种交易方向对应的BTC方向
            coin_direction = self.direction_mapping[action]
            
            # 计算方向一致性
            if coin_direction == btc_direction:
                # 方向一致，增强信号
                enhancement_factor = 1 + self.btc_weight * btc_confidence
                enhanced_confidence = base_confidence * enhancement_factor
                logger.debug(f"方向一致增强: {coin_direction} == {btc_direction}, 增强因子: {enhancement_factor:.3f}")
            elif (coin_direction == 'bullish' and btc_direction == 'bearish') or \
                 (coin_direction == 'bearish' and btc_direction == 'bullish'):
                # 方向相反，减弱信号
                reduction_factor = 1 - self.btc_weight * btc_confidence * 0.5  # 减弱程度较小
                enhanced_confidence = base_confidence * max(0.1, reduction_factor)  # 最低保持10%置信度
                logger.debug(f"方向相反减弱: {coin_direction} != {btc_direction}, 减弱因子: {reduction_factor:.3f}")
            else:
                # 其他情况（如一个是sideways），轻微调整
                if btc_direction == 'sideways':
                    # BTC震荡时，对非持有信号轻微减弱
                    if action != 'HOLD':
                        adjustment_factor = 1 - self.btc_weight * btc_confidence * 0.2
                        enhanced_confidence = base_confidence * adjustment_factor
                        logger.debug(f"BTC震荡调整: 减弱因子: {adjustment_factor:.3f}")
                    else:
                        enhanced_confidence = base_confidence
                else:
                    enhanced_confidence = base_confidence
            
            # 确保置信度在合理范围内
            enhanced_confidence = max(0.0, min(1.0, enhanced_confidence))
            
            return enhanced_confidence
            
        except Exception as e:
            logger.error(f"信号增强算法执行失败: {e}")
            return base_confidence
    
    def predict(self, data: Dict[str, Any]) -> PredictionResult:
        """
        执行交易决策预测
        
        Args:
            data: 输入数据，包含币种OHLCV数据、技术指标和BTC预测信号
                  格式：{
                      'symbol': 'BTC/USDT',
                      'ohlcv_data': [[open, high, low, close, volume], ...],  # 24个时间步
                      'technical_indicators': [[rsi, macd, ...], ...],        # 24个时间步的技术指标
                      'btc_signal': {                                         # 可选的BTC预测信号
                          'direction': 'bullish',
                          'confidence': 0.8,
                          'metadata': {'probabilities': {...}}
                      },
                      'timestamp': '2024-01-01T00:00:00Z'                     # 可选的时间戳
                  }
                  
        Returns:
            PredictionResult: 交易决策预测结果
        """
        if not self.is_model_loaded():
            raise RuntimeError("交易决策AI模型未加载，无法执行预测")
        
        if not self.validate_input_data(data):
            raise ValueError("交易决策AI模型输入数据格式错误")
        
        start_time = time.time()
        
        try:
            # 预处理输入数据
            model_input = self._preprocess_input(data)
            
            # 执行ONNX模型推理
            input_name = self._onnx_session.get_inputs()[0].name
            output_name = self._onnx_session.get_outputs()[0].name
            
            model_output = self._onnx_session.run(
                [output_name],
                {input_name: model_input}
            )[0]
            
            # 后处理输出
            result = self._postprocess_output(model_output, data)
            
            # 更新性能统计
            inference_time = (time.time() - start_time) * 1000  # 转换为毫秒
            self._inference_count += 1
            self._total_inference_time += inference_time
            
            result.metadata['inference_time_ms'] = inference_time
            
            # 检查延迟要求
            if inference_time > self.inference_timeout_ms:
                logger.warning(f"交易AI推理延迟超标: {inference_time:.2f}ms > {self.inference_timeout_ms}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"交易决策预测失败: {e}")
            raise RuntimeError(f"交易决策预测失败: {e}")
    
    def get_model_info(self) -> ModelInfo:
        """
        获取交易决策AI模型信息
        
        Returns:
            ModelInfo: 模型详细信息
        """
        if not self.is_model_loaded():
            raise RuntimeError("模型未加载，无法获取模型信息")
        
        # 获取模型文件大小
        model_size = None
        try:
            model_size = Path(self.model_path).stat().st_size
        except OSError:
            pass
        
        # 计算平均推理时间
        avg_inference_time = 0.0
        if self._inference_count > 0:
            avg_inference_time = self._total_inference_time / self._inference_count
        
        # 构建性能指标
        performance_metrics = {
            'inference_count': self._inference_count,
            'total_inference_time_ms': self._total_inference_time,
            'avg_inference_time_ms': avg_inference_time,
            'inference_timeout_ms': self.inference_timeout_ms,
            'sequence_length': self.sequence_length,
            'total_features': self.total_features,
            'output_classes': self.output_classes,
            'btc_weight': self.btc_weight
        }
        
        # 从元数据获取额外信息
        description = self._model_metadata.get(
            'description', 
            '交易决策AI模型 - 基于币种数据和BTC预测信号生成交易决策'
        )
        
        return ModelInfo(
            name="TradingAIModel",
            version=self.model_version,
            model_path=self.model_path,
            model_type="onnx",
            created_at=self._model_metadata.get('created_at', datetime.now(timezone.utc).isoformat()),
            model_size=model_size,
            description=description,
            input_shape=[1, self.sequence_length, self.total_features],
            output_shape=[1, self.output_classes],
            performance_metrics=performance_metrics
        )


class TradingAI:
    """
    交易决策AI高级接口
    
    整合TradingAIModel、数据获取、缓存和信号增强功能，
    提供完整的交易决策解决方案。支持：
    - 实时交易决策生成
    - BTC预测信号集成
    - 多币种并发处理
    - 性能监控和优化
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易决策AI
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 时区设置
        self.timezone = pytz.timezone(config.get("system", {}).get("timezone", "Asia/Shanghai"))
        
        # 核心组件
        self.model: Optional[TradingAIModel] = None
        self.data_provider = None  # 延迟导入避免循环依赖
        self.cache_interface = None
        
        # 配置参数
        self.trading_config = config.get("ai_models", {}).get("trading_ai", {})
        self.monitoring_config = config.get("monitoring", {})
        
        # 性能阈值
        self.latency_thresholds = self.monitoring_config.get("latency_thresholds", {})
        self.mainstream_threshold = self.latency_thresholds.get("mainstream_coins", 100)
        self.other_threshold = self.latency_thresholds.get("other_coins", 300)
        
        # 主流币种列表
        self.mainstream_coins = {
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT',
            'SOL/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT'
        }
        
        # 状态标志
        self.is_initialized = False
        
        # 统计信息
        self.prediction_stats = {
            "total_predictions": 0,
            "successful_predictions": 0,
            "failed_predictions": 0,
            "avg_inference_time_ms": 0.0,
            "timeout_count": 0,
            "enhancement_applied_count": 0,
            "last_prediction_time": None,
            "last_error": None
        }
        
        self.logger.info("交易决策AI初始化完成")
    
    async def initialize(self) -> bool:
        """
        初始化交易决策AI的所有组件
        
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        try:
            self.logger.info("开始初始化交易决策AI...")
            
            # 1. 初始化AI模型
            model_config = self.trading_config.copy()
            model_config.update(self.config)  # 传递完整配置
            self.model = TradingAIModel(model_config)
            
            if not self.model.load_model():
                raise RuntimeError("交易决策AI模型加载失败")
            
            self.logger.info("交易决策AI模型加载成功")
            
            # 2. 初始化数据提供器（延迟导入）
            from ..data.nautilus_data_provider import NautilusDataProvider
            self.data_provider = NautilusDataProvider(self.config)
            
            if not await self.data_provider.initialize():
                raise RuntimeError("数据提供器初始化失败")
            
            self.logger.info("数据提供器初始化成功")
            
            # 3. 初始化缓存接口
            from ..data.cache_factory import get_cache
            self.cache_interface = get_cache()
            
            if not self.cache_interface or not self.cache_interface.is_connected():
                self.logger.warning("缓存接口不可用，BTC预测信号将无法获取")
            else:
                self.logger.info("缓存接口初始化成功")
            
            self.is_initialized = True
            self.logger.info("交易决策AI初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"交易决策AI初始化失败: {e}")
            self.is_initialized = False
            return False
    
    async def predict_trading_signal(self, symbol: str, timeframe: str = "1-MINUTE", 
                                   bars_count: int = 24) -> Optional[Dict[str, Any]]:
        """
        生成指定币种的交易信号
        
        Args:
            symbol: 交易对符号，如 'BTC/USDT'
            timeframe: 时间框架，默认1分钟
            bars_count: 历史数据条数，默认24条
            
        Returns:
            Optional[Dict[str, Any]]: 交易信号字典，失败返回None
        """
        if not self.is_initialized:
            self.logger.error("交易决策AI未初始化，无法生成交易信号")
            return None
        
        start_time = time.time()
        
        try:
            self.logger.info(f"开始生成交易信号: {symbol}")
            
            # 1. 获取币种历史数据
            coin_data = await self.data_provider.get_historical_bars(
                symbol=symbol,
                timeframe=timeframe,
                count=bars_count
            )
            
            if not coin_data or len(coin_data) < bars_count:
                raise ValueError(f"获取{symbol}历史数据失败或数据不足")
            
            # 2. 获取BTC预测信号
            btc_signal = None
            if self.cache_interface and self.cache_interface.is_connected():
                try:
                    btc_prediction = self.cache_interface.get_btc_prediction()
                    if btc_prediction:
                        btc_signal = btc_prediction.to_dict()
                        self.logger.debug(f"获取到BTC预测信号: {btc_signal['direction']}")
                    else:
                        self.logger.debug("未获取到BTC预测信号")
                except Exception as e:
                    self.logger.warning(f"获取BTC预测信号失败: {e}")
            
            # 3. 准备模型输入数据
            model_input = self._prepare_model_input(coin_data, symbol, btc_signal)
            
            # 4. 执行模型预测
            prediction_result = self.model.predict(model_input)
            
            # 5. 转换为交易信号格式
            trading_signal = self._convert_to_trading_signal(prediction_result, symbol)
            
            # 6. 更新统计信息
            inference_time = (time.time() - start_time) * 1000
            self._update_prediction_stats(True, inference_time, btc_signal is not None)
            
            # 7. 检查延迟要求
            threshold = self.mainstream_threshold if symbol in self.mainstream_coins else self.other_threshold
            if inference_time > threshold:
                self.logger.warning(f"交易信号生成延迟超标 {symbol}: {inference_time:.2f}ms > {threshold}ms")
                self.prediction_stats["timeout_count"] += 1
            
            self.logger.info(f"成功生成交易信号 {symbol}: {trading_signal['action']} (延迟: {inference_time:.2f}ms)")
            return trading_signal
            
        except Exception as e:
            # 更新失败统计
            inference_time = (time.time() - start_time) * 1000
            self._update_prediction_stats(False, inference_time, False, str(e))
            
            self.logger.error(f"生成交易信号失败 {symbol}: {e}")
            return None
    
    def _prepare_model_input(self, coin_data: List[Dict[str, Any]], symbol: str, 
                           btc_signal: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        准备模型输入数据
        
        Args:
            coin_data: 币种历史数据
            symbol: 交易对符号
            btc_signal: BTC预测信号
            
        Returns:
            Dict[str, Any]: 模型输入数据
        """
        # 提取OHLCV数据
        ohlcv_data = []
        for bar in coin_data:
            ohlcv_data.append([
                bar['open'],
                bar['high'],
                bar['low'],
                bar['close'],
                bar['volume']
            ])
        
        # 计算技术指标（这里使用简化的技术指标计算）
        tech_indicators = self._calculate_technical_indicators(coin_data)
        
        # 构建模型输入
        model_input = {
            'symbol': symbol,
            'ohlcv_data': ohlcv_data,
            'technical_indicators': tech_indicators,
            'btc_signal': btc_signal,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        return model_input
    
    def _calculate_technical_indicators(self, coin_data: List[Dict[str, Any]]) -> List[List[float]]:
        """
        计算技术指标
        
        Args:
            coin_data: 币种历史数据
            
        Returns:
            List[List[float]]: 技术指标数据，形状为(24, 20)
        """
        # 提取价格数据
        closes = [bar['close'] for bar in coin_data]
        highs = [bar['high'] for bar in coin_data]
        lows = [bar['low'] for bar in coin_data]
        volumes = [bar['volume'] for bar in coin_data]
        
        tech_indicators = []
        
        for i in range(len(coin_data)):
            indicators = []
            
            # 简单移动平均线 (SMA)
            if i >= 4:
                sma_5 = sum(closes[i-4:i+1]) / 5
                sma_10 = sum(closes[max(0, i-9):i+1]) / min(10, i+1)
                sma_20 = sum(closes[max(0, i-19):i+1]) / min(20, i+1)
            else:
                sma_5 = closes[i]
                sma_10 = closes[i]
                sma_20 = closes[i]
            
            indicators.extend([sma_5, sma_10, sma_20])
            
            # RSI (简化版本)
            if i >= 13:
                gains = []
                losses = []
                for j in range(i-13, i):
                    change = closes[j+1] - closes[j]
                    if change > 0:
                        gains.append(change)
                        losses.append(0)
                    else:
                        gains.append(0)
                        losses.append(-change)
                
                avg_gain = sum(gains) / 14
                avg_loss = sum(losses) / 14
                
                if avg_loss == 0:
                    rsi = 100
                else:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 50  # 中性值
            
            indicators.append(rsi)
            
            # MACD (简化版本)
            if i >= 25:
                ema_12 = closes[i]  # 简化为当前价格
                ema_26 = closes[i]  # 简化为当前价格
                macd = ema_12 - ema_26
                signal = macd  # 简化
                histogram = macd - signal
            else:
                macd = 0
                signal = 0
                histogram = 0
            
            indicators.extend([macd, signal, histogram])
            
            # 布林带 (简化版本)
            if i >= 19:
                sma_20_bb = sum(closes[i-19:i+1]) / 20
                variance = sum([(close - sma_20_bb) ** 2 for close in closes[i-19:i+1]]) / 20
                std_dev = variance ** 0.5
                upper_band = sma_20_bb + (2 * std_dev)
                lower_band = sma_20_bb - (2 * std_dev)
                bb_position = (closes[i] - lower_band) / (upper_band - lower_band) if upper_band != lower_band else 0.5
            else:
                upper_band = closes[i]
                lower_band = closes[i]
                bb_position = 0.5
            
            indicators.extend([upper_band, lower_band, bb_position])
            
            # 成交量指标
            if i >= 9:
                vol_sma_10 = sum(volumes[i-9:i+1]) / 10
                vol_ratio = volumes[i] / vol_sma_10 if vol_sma_10 > 0 else 1
            else:
                vol_ratio = 1
            
            indicators.append(vol_ratio)
            
            # ATR (简化版本)
            if i >= 13:
                true_ranges = []
                for j in range(i-13, i):
                    tr1 = highs[j+1] - lows[j+1]
                    tr2 = abs(highs[j+1] - closes[j])
                    tr3 = abs(lows[j+1] - closes[j])
                    true_ranges.append(max(tr1, tr2, tr3))
                atr = sum(true_ranges) / 14
            else:
                atr = highs[i] - lows[i]
            
            indicators.append(atr)
            
            # 价格变化率
            if i >= 1:
                price_change = (closes[i] - closes[i-1]) / closes[i-1]
            else:
                price_change = 0
            
            indicators.append(price_change)
            
            # 添加更多技术指标以达到29个
            # 威廉指标 %R
            if i >= 13:
                highest_high = max(highs[i-13:i+1])
                lowest_low = min(lows[i-13:i+1])
                if highest_high != lowest_low:
                    williams_r = (highest_high - closes[i]) / (highest_high - lowest_low) * -100
                else:
                    williams_r = -50
            else:
                williams_r = -50
            indicators.append(williams_r)
            
            # 随机指标 %K
            if i >= 13:
                highest_high = max(highs[i-13:i+1])
                lowest_low = min(lows[i-13:i+1])
                if highest_high != lowest_low:
                    stoch_k = (closes[i] - lowest_low) / (highest_high - lowest_low) * 100
                else:
                    stoch_k = 50
            else:
                stoch_k = 50
            indicators.append(stoch_k)
            
            # 商品通道指数 CCI
            if i >= 19:
                typical_prices = [(highs[j] + lows[j] + closes[j]) / 3 for j in range(i-19, i+1)]
                sma_tp = sum(typical_prices) / 20
                mean_deviation = sum([abs(tp - sma_tp) for tp in typical_prices]) / 20
                if mean_deviation != 0:
                    cci = (typical_prices[-1] - sma_tp) / (0.015 * mean_deviation)
                else:
                    cci = 0
            else:
                cci = 0
            indicators.append(cci)
            
            # 动量指标
            if i >= 9:
                momentum = closes[i] - closes[i-9]
            else:
                momentum = 0
            indicators.append(momentum)
            
            # 变化率 ROC
            if i >= 11:
                roc = (closes[i] - closes[i-11]) / closes[i-11] * 100
            else:
                roc = 0
            indicators.append(roc)
            
            # 平均真实波幅比率
            if i >= 1 and atr != 0:
                atr_ratio = (highs[i] - lows[i]) / atr
            else:
                atr_ratio = 1
            indicators.append(atr_ratio)
            
            # 价格位置指标（相对于最近20期的高低点）
            if i >= 19:
                period_high = max(highs[i-19:i+1])
                period_low = min(lows[i-19:i+1])
                if period_high != period_low:
                    price_position = (closes[i] - period_low) / (period_high - period_low)
                else:
                    price_position = 0.5
            else:
                price_position = 0.5
            indicators.append(price_position)
            
            # 成交量变化率
            if i >= 1 and volumes[i-1] != 0:
                volume_change = (volumes[i] - volumes[i-1]) / volumes[i-1]
            else:
                volume_change = 0
            indicators.append(volume_change)
            
            # 价格波动率（标准差）
            if i >= 9:
                price_std = np.std(closes[i-9:i+1])
            else:
                price_std = 0
            indicators.append(price_std)
            
            # 填充到29个指标
            while len(indicators) < 29:
                indicators.append(0.0)
            
            # 截断到29个指标
            indicators = indicators[:29]
            
            tech_indicators.append(indicators)
        
        return tech_indicators
    
    def _convert_to_trading_signal(self, prediction_result: PredictionResult, symbol: str) -> Dict[str, Any]:
        """
        将预测结果转换为交易信号格式
        
        Args:
            prediction_result: 模型预测结果
            symbol: 交易对符号
            
        Returns:
            Dict[str, Any]: 交易信号字典
        """
        # 从元数据中获取交易动作
        action = prediction_result.metadata.get('action', 'HOLD')
        
        trading_signal = {
            'symbol': symbol,
            'action': action,
            'direction': prediction_result.direction,
            'confidence': prediction_result.confidence,
            'base_confidence': prediction_result.metadata.get('base_confidence', prediction_result.confidence),
            'enhanced_confidence': prediction_result.metadata.get('enhanced_confidence', prediction_result.confidence),
            'btc_enhancement_applied': prediction_result.metadata.get('btc_enhancement_applied', False),
            'timestamp': prediction_result.timestamp,
            'valid_until': prediction_result.valid_until,
            'inference_time_ms': prediction_result.metadata.get('inference_time_ms', 0),
            'probabilities': prediction_result.metadata.get('probabilities', {}),
            'model_version': prediction_result.metadata.get('model_version', 'unknown'),
            'prediction_horizon': prediction_result.metadata.get('prediction_horizon', '5m'),
            'metadata': prediction_result.metadata
        }
        
        return trading_signal
    
    def _update_prediction_stats(self, success: bool, inference_time: float, 
                               enhancement_applied: bool, error_msg: Optional[str] = None) -> None:
        """
        更新预测统计信息
        
        Args:
            success: 预测是否成功
            inference_time: 推理时间（毫秒）
            enhancement_applied: 是否应用了BTC信号增强
            error_msg: 错误信息（如果失败）
        """
        self.prediction_stats["total_predictions"] += 1
        
        if success:
            self.prediction_stats["successful_predictions"] += 1
            self.prediction_stats["last_error"] = None
        else:
            self.prediction_stats["failed_predictions"] += 1
            self.prediction_stats["last_error"] = error_msg
        
        if enhancement_applied:
            self.prediction_stats["enhancement_applied_count"] += 1
        
        # 更新平均推理时间
        total_time = (self.prediction_stats["avg_inference_time_ms"] * 
                     (self.prediction_stats["total_predictions"] - 1) + inference_time)
        self.prediction_stats["avg_inference_time_ms"] = total_time / self.prediction_stats["total_predictions"]
        
        self.prediction_stats["last_prediction_time"] = datetime.now(self.timezone)
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取交易决策AI状态信息
        
        Returns:
            Dict[str, Any]: 状态信息字典
        """
        status = {
            "is_initialized": self.is_initialized,
            "timezone": str(self.timezone),
            "model_loaded": self.model.is_model_loaded() if self.model else False,
            "data_provider_initialized": self.data_provider.is_initialized if self.data_provider else False,
            "cache_available": self.cache_interface.is_connected() if self.cache_interface else False,
            "mainstream_threshold_ms": self.mainstream_threshold,
            "other_threshold_ms": self.other_threshold,
            "mainstream_coins_count": len(self.mainstream_coins)
        }
        
        # 添加统计信息
        status.update(self.prediction_stats)
        
        # 计算成功率
        if self.prediction_stats["total_predictions"] > 0:
            status["success_rate"] = (self.prediction_stats["successful_predictions"] / 
                                    self.prediction_stats["total_predictions"])
            status["enhancement_rate"] = (self.prediction_stats["enhancement_applied_count"] / 
                                        self.prediction_stats["total_predictions"])
        else:
            status["success_rate"] = 0.0
            status["enhancement_rate"] = 0.0
        
        return status
    
    async def cleanup(self) -> None:
        """
        清理交易决策AI资源
        """
        try:
            self.logger.info("开始清理交易决策AI资源...")
            
            # 清理数据提供器
            if self.data_provider:
                await self.data_provider.shutdown()
            
            # 卸载模型
            if self.model:
                self.model.unload_model()
            
            # 重置状态
            self.is_initialized = False
            
            self.logger.info("交易决策AI资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理交易决策AI资源失败: {e}")
    
    def __str__(self) -> str:
        """
        返回交易决策AI的字符串表示
        """
        status = "已初始化" if self.is_initialized else "未初始化"
        return f"交易决策AI - 状态: {status}, 时区: {self.timezone}"
    
    def __repr__(self) -> str:
        """
        返回交易决策AI的详细字符串表示
        """
        return f"TradingAI(initialized={self.is_initialized}, timezone='{self.timezone}')"