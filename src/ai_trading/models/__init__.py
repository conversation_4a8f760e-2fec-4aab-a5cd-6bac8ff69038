"""
AI模型模块

本模块提供AI模型的接口定义和具体实现，包括：
- AIModelInterface: AI模型抽象接口基类
- PredictionResult: 预测结果数据类
- ModelInfo: 模型信息数据类

支持ONNX和Pickle格式的模型加载，为BTC预测AI和交易决策AI提供统一的接口规范。
"""

from .interfaces import (
    AIModelInterface,
    PredictionResult,
    ModelInfo
)
from .base_model import BaseAIModel
from .btc_predictor import BTCPredictorModel, BTCPredictor
from .trading_ai import TradingAIModel, TradingAI

__all__ = [
    'AIModelInterface',
    'PredictionResult', 
    'ModelInfo',
    'BaseAIModel',
    'BTCPredictorModel',
    'BTCPredictor',
    'TradingAIModel',
    'TradingAI'
]