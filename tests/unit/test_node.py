"""
Nautilus TradingNode初始化模块单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import tempfile
from pathlib import Path
import yaml

from src.ai_trading.core.node import AITradingNode, get_trading_node, initialize_trading_system
from src.ai_trading.core.config import AITradingConfig, SystemConfig, RedisConfig


class TestAITradingNode:
    """AI交易节点测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建测试配置
        self.test_config = AITradingConfig(
            system=SystemConfig(
                timezone="Asia/Shanghai",
                log_level="INFO",
                trader_id="TEST_TRADER"
            ),
            ai_models={},
            redis=RedisConfig(
                host="localhost",
                port=6379,
                db=0
            ),
            sqlite=Mock(),
            risk_management=Mock(),
            strategies=Mock(),
            monitoring=Mock(),
            nautilus={}
        )
        
        # 重置全局实例
        import src.ai_trading.core.node as node_module
        node_module._trading_node = None
    
    def test_init(self):
        """测试节点初始化"""
        node = AITradingNode(self.test_config)
        
        assert node._config == self.test_config
        assert node._node is None
        assert node._is_running is False
        assert node._connection_status == {}
    
    def test_properties_before_build(self):
        """测试构建前访问属性抛出异常"""
        node = AITradingNode(self.test_config)
        
        with pytest.raises(RuntimeError, match="节点未初始化"):
            _ = node.data_engine
        
        with pytest.raises(RuntimeError, match="节点未初始化"):
            _ = node.exec_engine
        
        with pytest.raises(RuntimeError, match="节点未初始化"):
            _ = node.risk_engine
        
        with pytest.raises(RuntimeError, match="节点未初始化"):
            _ = node.cache
        
        with pytest.raises(RuntimeError, match="节点未初始化"):
            _ = node.portfolio
    
    @patch('src.ai_trading.core.node.TradingNode')
    @patch('src.ai_trading.core.node.TradingNodeConfig')
    def test_build_success(self, mock_config_class, mock_node_class):
        """测试成功构建节点"""
        # 设置mock
        mock_config = Mock()
        mock_config_class.return_value = mock_config
        
        mock_node = Mock()
        mock_node_class.return_value = mock_node
        
        # 创建节点并构建
        node = AITradingNode(self.test_config)
        node.build()
        
        # 验证调用
        mock_config_class.assert_called_once()
        mock_node_class.assert_called_once_with(config=mock_config)
        mock_node.build.assert_called_once()
        
        # 验证状态
        assert node._node == mock_node
    
    @patch('src.ai_trading.core.node.TradingNode')
    def test_build_failure(self, mock_node_class):
        """测试构建节点失败"""
        # 设置mock抛出异常
        mock_node_class.side_effect = Exception("构建失败")
        
        node = AITradingNode(self.test_config)
        
        with pytest.raises(RuntimeError, match="节点构建失败"):
            node.build()
    
    @pytest.mark.asyncio
    async def test_start_without_build(self):
        """测试未构建就启动节点"""
        node = AITradingNode(self.test_config)
        
        with pytest.raises(RuntimeError, match="节点未构建"):
            await node.start()
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.TradingNode')
    @patch('src.ai_trading.core.node.TradingNodeConfig')
    async def test_start_success(self, mock_config_class, mock_node_class):
        """测试成功启动节点"""
        # 设置mock
        mock_node = AsyncMock()
        mock_node_class.return_value = mock_node
        
        # 模拟引擎属性
        mock_node.kernel.data_engine = Mock()
        mock_node.kernel.exec_engine = Mock()
        mock_node.kernel.risk_engine = Mock()
        
        # 创建、构建和启动节点
        node = AITradingNode(self.test_config)
        node.build()
        await node.start()
        
        # 验证调用
        mock_node.start_async.assert_called_once()
        
        # 验证状态
        assert node._is_running is True
        assert node.is_running is True
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.TradingNode')
    @patch('src.ai_trading.core.node.TradingNodeConfig')
    async def test_start_failure(self, mock_config_class, mock_node_class):
        """测试启动节点失败"""
        # 设置mock
        mock_node = AsyncMock()
        mock_node.start_async.side_effect = Exception("启动失败")
        mock_node_class.return_value = mock_node
        
        # 创建和构建节点
        node = AITradingNode(self.test_config)
        node.build()
        
        with pytest.raises(RuntimeError, match="节点启动失败"):
            await node.start()
        
        # 验证状态
        assert node._is_running is False
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.TradingNode')
    @patch('src.ai_trading.core.node.TradingNodeConfig')
    async def test_stop_success(self, mock_config_class, mock_node_class):
        """测试成功停止节点"""
        # 设置mock
        mock_node = AsyncMock()
        mock_node_class.return_value = mock_node
        mock_node.kernel.data_engine = Mock()
        mock_node.kernel.exec_engine = Mock()
        mock_node.kernel.risk_engine = Mock()
        
        # 创建、构建和启动节点
        node = AITradingNode(self.test_config)
        node.build()
        await node.start()
        
        # 停止节点
        await node.stop()
        
        # 验证调用
        mock_node.stop_async.assert_called_once()
        
        # 验证状态
        assert node._is_running is False
    
    @pytest.mark.asyncio
    async def test_stop_not_running(self):
        """测试停止未运行的节点"""
        node = AITradingNode(self.test_config)
        
        # 应该不抛出异常
        await node.stop()
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.TradingNode')
    @patch('src.ai_trading.core.node.TradingNodeConfig')
    async def test_restart(self, mock_config_class, mock_node_class):
        """测试重启节点"""
        # 设置mock
        mock_node = AsyncMock()
        mock_node_class.return_value = mock_node
        mock_node.kernel.data_engine = Mock()
        mock_node.kernel.exec_engine = Mock()
        mock_node.kernel.risk_engine = Mock()
        
        # 创建、构建和启动节点
        node = AITradingNode(self.test_config)
        node.build()
        await node.start()
        
        # 重启节点
        await node.restart()
        
        # 验证调用（停止和启动各一次）
        assert mock_node.stop_async.call_count == 1
        assert mock_node.start_async.call_count == 2  # 启动 + 重启
    
    def test_create_node_config(self):
        """测试创建节点配置"""
        node = AITradingNode(self.test_config)
        
        # 调用私有方法
        config = node._create_node_config()
        
        # 验证配置内容
        assert config.trader_id == "TEST_TRADER"
        assert config.log_level == "INFO"
        assert "cache" in config.__dict__
        assert "data_clients" in config.__dict__
        assert "exec_clients" in config.__dict__
    
    def test_merge_config(self):
        """测试配置合并"""
        node = AITradingNode(self.test_config)
        
        base_config = {
            "a": 1,
            "b": {
                "c": 2,
                "d": 3
            }
        }
        
        user_config = {
            "b": {
                "d": 4,
                "e": 5
            },
            "f": 6
        }
        
        node._merge_config(base_config, user_config)
        
        expected = {
            "a": 1,
            "b": {
                "c": 2,
                "d": 4,  # 被覆盖
                "e": 5   # 新增
            },
            "f": 6  # 新增
        }
        
        assert base_config == expected
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.TradingNode')
    @patch('src.ai_trading.core.node.TradingNodeConfig')
    async def test_health_check(self, mock_config_class, mock_node_class):
        """测试健康检查"""
        # 设置mock
        mock_node = AsyncMock()
        mock_node_class.return_value = mock_node
        mock_node.kernel.data_engine = Mock()
        mock_node.kernel.exec_engine = Mock()
        mock_node.kernel.risk_engine = Mock()
        mock_node.kernel.cache = Mock()
        mock_node.kernel.portfolio = Mock()
        
        # 创建、构建和启动节点
        node = AITradingNode(self.test_config)
        node.build()
        await node.start()
        
        # 执行健康检查
        health = await node.health_check()
        
        # 验证健康检查结果
        assert health["node_running"] is True
        assert "connections" in health
        assert health["data_engine_status"] == "healthy"
        assert health["exec_engine_status"] == "healthy"
        assert health["risk_engine_status"] == "healthy"
        assert health["cache_status"] == "healthy"
        assert health["portfolio_status"] == "healthy"
    
    def test_get_trading_node_singleton(self):
        """测试交易节点单例模式"""
        # 重置全局实例
        import src.ai_trading.core.node as node_module
        node_module._trading_node = None
        
        # 获取两个实例
        node1 = get_trading_node(self.test_config)
        node2 = get_trading_node()
        
        # 验证是同一个实例
        assert node1 is node2
        
        # 清理
        node_module._trading_node = None


class TestInitializationFunctions:
    """初始化函数测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.yaml"
        
        test_config = {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO",
                "trader_id": "TEST_TRADER"
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0
            },
            "ai_models": {},
            "sqlite": {"db_path": "data/test.db"},
            "risk_management": {"max_trade_risk": 0.02},
            "strategies": {},
            "monitoring": {}
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
        # 重置全局实例
        import src.ai_trading.core.node as node_module
        node_module._trading_node = None
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.AITradingNode')
    @patch('src.ai_trading.core.logging.setup_logging')
    async def test_initialize_trading_system_success(self, mock_setup_logging, mock_node_class):
        """测试成功初始化交易系统"""
        # 设置mock
        mock_node = AsyncMock()
        mock_node_class.return_value = mock_node
        
        # 初始化系统
        result = await initialize_trading_system(str(self.config_file))
        
        # 验证调用
        mock_setup_logging.assert_called_once()
        mock_node.build.assert_called_once()
        mock_node.start.assert_called_once()
        
        # 验证返回值
        assert result == mock_node
    
    @pytest.mark.asyncio
    @patch('src.ai_trading.core.node.AITradingNode')
    async def test_initialize_trading_system_failure(self, mock_node_class):
        """测试初始化交易系统失败"""
        # 设置mock抛出异常
        mock_node = AsyncMock()
        mock_node.build.side_effect = Exception("构建失败")
        mock_node_class.return_value = mock_node
        
        with pytest.raises(RuntimeError, match="系统初始化失败"):
            await initialize_trading_system(str(self.config_file))


if __name__ == "__main__":
    pytest.main([__file__, "-v"])