"""
配置管理模块单元测试
"""

import pytest
import tempfile
import os
from pathlib import Path
import yaml

from src.ai_trading.core.config import (
    ConfigManager, 
    AITradingConfig, 
    SystemConfig,
    AIModelConfig,
    RedisConfig,
    get_config_manager
)


class TestConfigManager:
    """配置管理器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.yaml"
        
        # 测试配置数据
        self.test_config = {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO",
                "trader_id": "TEST_TRADER"
            },
            "ai_models": {
                "btc_predictor": {
                    "model_path": "models/btc_predictor/btc_predictor.onnx",
                    "backup_path": "models/backup/btc_predictor",
                    "version": "1.0.0",
                    "update_interval": 3600,
                    "input_features": ["open", "high", "low", "close", "volume"]
                },
                "trading_ai": {
                    "model_path": "models/trading_ai/trading_ai.onnx",
                    "backup_path": "models/backup/trading_ai",
                    "version": "1.0.0",
                    "inference_timeout": 100,
                    "input_features": ["ohlcv", "btc_signal"]
                }
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None,
                "connection_timeout": 5
            },
            "sqlite": {
                "db_path": "data/test_ai_trading.db",
                "backup_interval": 3600
            },
            "risk_management": {
                "max_trade_risk": 0.02,
                "exposure_limit": 0.15,
                "max_leverage": 10,
                "volatility_threshold": 0.1,
                "circuit_breaker": {
                    "daily_loss_limit": 0.05
                }
            },
            "strategies": {
                "trend_following": {
                    "enabled": True,
                    "confidence_threshold": 0.6
                },
                "grid_trading": {
                    "enabled": True,
                    "base_spacing": 0.005
                }
            },
            "monitoring": {
                "latency_thresholds": {
                    "mainstream_coins": 100,
                    "other_coins": 300
                },
                "performance_metrics": ["ai_inference_latency", "trade_success_rate"]
            }
        }
        
        # 写入测试配置文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f, default_flow_style=False, allow_unicode=True)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_load_config_success(self):
        """测试成功加载配置"""
        config_manager = ConfigManager(str(self.config_file))
        config = config_manager.load_config()
        
        # 验证配置类型
        assert isinstance(config, AITradingConfig)
        
        # 验证系统配置
        assert config.system.timezone == "Asia/Shanghai"
        assert config.system.log_level == "INFO"
        assert config.system.trader_id == "TEST_TRADER"
        
        # 验证AI模型配置
        assert "btc_predictor" in config.ai_models
        assert "trading_ai" in config.ai_models
        
        btc_model = config.ai_models["btc_predictor"]
        assert isinstance(btc_model, AIModelConfig)
        assert btc_model.model_path == "models/btc_predictor/btc_predictor.onnx"
        assert btc_model.version == "1.0.0"
        
        # 验证Redis配置
        assert isinstance(config.redis, RedisConfig)
        assert config.redis.host == "localhost"
        assert config.redis.port == 6379
        
        # 验证风险管理配置
        assert config.risk_management.max_trade_risk == 0.02
        assert config.risk_management.exposure_limit == 0.15
        assert config.risk_management.max_leverage == 10
    
    def test_load_config_file_not_found(self):
        """测试配置文件不存在的情况"""
        config_manager = ConfigManager("nonexistent_config.yaml")
        
        with pytest.raises(FileNotFoundError):
            config_manager.load_config()
    
    def test_load_config_invalid_yaml(self):
        """测试无效YAML格式"""
        # 创建无效的YAML文件
        invalid_config_file = Path(self.temp_dir) / "invalid_config.yaml"
        with open(invalid_config_file, 'w') as f:
            f.write("invalid: yaml: content: [")
        
        config_manager = ConfigManager(str(invalid_config_file))
        
        with pytest.raises(yaml.YAMLError):
            config_manager.load_config()
    
    def test_env_override(self):
        """测试环境变量覆盖"""
        # 设置环境变量
        os.environ["AI_TRADING_REDIS_HOST"] = "test-redis"
        os.environ["AI_TRADING_REDIS_PORT"] = "6380"
        os.environ["AI_TRADING_LOG_LEVEL"] = "DEBUG"
        
        try:
            config_manager = ConfigManager(str(self.config_file))
            config = config_manager.load_config()
            
            # 验证环境变量覆盖生效
            assert config.redis.host == "test-redis"
            assert config.redis.port == 6380
            assert config.system.log_level == "DEBUG"
            
        finally:
            # 清理环境变量
            for key in ["AI_TRADING_REDIS_HOST", "AI_TRADING_REDIS_PORT", "AI_TRADING_LOG_LEVEL"]:
                if key in os.environ:
                    del os.environ[key]
    
    def test_config_validation_invalid_timezone(self):
        """测试无效时区验证"""
        # 修改配置为无效时区
        invalid_config = self.test_config.copy()
        invalid_config["system"]["timezone"] = "Invalid/Timezone"
        
        invalid_config_file = Path(self.temp_dir) / "invalid_timezone_config.yaml"
        with open(invalid_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(invalid_config, f)
        
        config_manager = ConfigManager(str(invalid_config_file))
        
        with pytest.raises(ValueError, match="无效的时区"):
            config_manager.load_config()
    
    def test_config_validation_invalid_risk_params(self):
        """测试无效风险参数验证"""
        # 修改配置为无效风险参数
        invalid_config = self.test_config.copy()
        invalid_config["risk_management"]["max_trade_risk"] = 1.5  # 超过1.0
        
        invalid_config_file = Path(self.temp_dir) / "invalid_risk_config.yaml"
        with open(invalid_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(invalid_config, f)
        
        config_manager = ConfigManager(str(invalid_config_file))
        
        with pytest.raises(ValueError, match="max_trade_risk必须在"):
            config_manager.load_config()
    
    def test_reload_if_changed(self):
        """测试配置文件变化检测和重新加载"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 首次加载
        config1 = config_manager.load_config()
        assert config1.system.trader_id == "TEST_TRADER"
        
        # 修改配置文件
        modified_config = self.test_config.copy()
        modified_config["system"]["trader_id"] = "MODIFIED_TRADER"
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(modified_config, f)
        
        # 检测变化并重新加载
        reloaded = config_manager.reload_if_changed()
        assert reloaded is True
        
        # 验证配置已更新
        config2 = config_manager.get_config()
        assert config2.system.trader_id == "MODIFIED_TRADER"
    
    def test_hot_reload_callbacks(self):
        """测试热重载回调功能"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 回调测试变量
        callback_called = []
        
        def test_callback(new_config):
            callback_called.append(new_config.system.trader_id)
        
        # 添加回调
        config_manager.add_reload_callback(test_callback)
        
        # 首次加载
        config_manager.load_config()
        
        # 修改配置文件
        modified_config = self.test_config.copy()
        modified_config["system"]["trader_id"] = "CALLBACK_TEST"
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(modified_config, f)
        
        # 触发重新加载
        config_manager.reload_if_changed()
        
        # 验证回调被调用
        assert len(callback_called) == 1
        assert callback_called[0] == "CALLBACK_TEST"
        
        # 移除回调
        config_manager.remove_reload_callback(test_callback)
        
        # 再次修改配置
        modified_config["system"]["trader_id"] = "CALLBACK_TEST_2"
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(modified_config, f)
        
        config_manager.reload_if_changed()
        
        # 验证回调不再被调用
        assert len(callback_called) == 1
    
    def test_config_backup(self):
        """测试配置备份功能"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 创建备份
        backup_path = config_manager.backup_config()
        
        # 验证备份文件存在
        assert Path(backup_path).exists()
        
        # 验证备份内容
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_config = yaml.safe_load(f)
        
        assert backup_config["system"]["trader_id"] == "TEST_TRADER"
        
        # 清理备份文件
        Path(backup_path).unlink()
    
    def test_config_validation_comprehensive(self):
        """测试全面的配置验证"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 测试各种无效配置
        invalid_configs = [
            # 无效日志级别
            {**self.test_config, "system": {**self.test_config["system"], "log_level": "INVALID"}},
            # 空交易员ID
            {**self.test_config, "system": {**self.test_config["system"], "trader_id": ""}},
            # 无效Redis端口
            {**self.test_config, "redis": {**self.test_config["redis"], "port": 70000}},
            # 负数数据库索引
            {**self.test_config, "redis": {**self.test_config["redis"], "db": -1}},
        ]
        
        for i, invalid_config in enumerate(invalid_configs):
            invalid_file = Path(self.temp_dir) / f"invalid_{i}.yaml"
            with open(invalid_file, 'w', encoding='utf-8') as f:
                yaml.dump(invalid_config, f)
            
            test_manager = ConfigManager(str(invalid_file))
            with pytest.raises(ValueError):
                test_manager.load_config()
    
    def test_config_helper(self):
        """测试配置辅助工具"""
        from src.ai_trading.core.config import ConfigHelper
        
        config_manager = ConfigManager(str(self.config_file))
        config = config_manager.load_config()
        helper = ConfigHelper(config)
        
        # 测试时区获取
        timezone = helper.get_timezone()
        assert str(timezone) == "Asia/Shanghai"
        
        # 测试模型路径获取
        btc_path = helper.get_model_path("btc_predictor")
        expected_path = Path("models/btc_predictor/btc_predictor.onnx")
        assert btc_path == expected_path
        
        # 测试Redis URL生成
        redis_url = helper.get_redis_url()
        assert redis_url == "redis://localhost:6379/0"
        
        # 测试策略启用检查
        trend_enabled = helper.is_strategy_enabled("trend_following")
        assert trend_enabled is True
        
        # 测试延迟阈值获取
        mainstream_threshold = helper.get_latency_threshold("mainstream_coins")
        assert mainstream_threshold == 100
        
        other_threshold = helper.get_latency_threshold("other_coins")
        assert other_threshold == 300
        
        # 测试熔断级别计算
        level_0 = helper.get_circuit_breaker_level(0.02)  # 2%波动率
        assert level_0 == 0
        
        level_1 = helper.get_circuit_breaker_level(0.04)  # 4%波动率
        assert level_1 == 1
        
        # 测试杠杆调整判断
        should_adjust = helper.should_adjust_leverage(5.0, 6.0)  # 差异1.0 > 阈值
        assert should_adjust is True
        
        should_not_adjust = helper.should_adjust_leverage(5.0, 5.3)  # 差异0.3 < 阈值
        assert should_not_adjust is False
    
    def test_get_config_manager_singleton(self):
        """测试配置管理器单例模式"""
        # 重置全局实例
        import src.ai_trading.core.config as config_module
        config_module._config_manager = None
        
        # 获取两个实例
        manager1 = get_config_manager(str(self.config_file))
        manager2 = get_config_manager()
        
        # 验证是同一个实例
        assert manager1 is manager2
        
        # 清理
        config_module._config_manager = None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])