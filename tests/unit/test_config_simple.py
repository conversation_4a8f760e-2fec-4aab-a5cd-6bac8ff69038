"""
配置管理模块简化测试

专注于核心功能测试，避免复杂依赖
"""

import pytest
import tempfile
import os
from pathlib import Path
import yaml

# 直接导入模块进行测试
import sys
sys.path.insert(0, 'src')

from ai_trading.core.config import Config<PERSON>anager, AITradingConfig


class TestConfigManagerSimple:
    """配置管理器简化测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.yaml"
        
        # 简化的测试配置数据
        self.test_config = {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO",
                "trader_id": "TEST_TRADER"
            },
            "ai_models": {
                "btc_predictor": {
                    "model_path": "models/btc_predictor/btc_predictor.onnx",
                    "backup_path": "models/backup/btc_predictor",
                    "version": "1.0.0"
                }
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0
            },
            "sqlite": {
                "db_path": "data/test.db"
            },
            "risk_management": {
                "max_trade_risk": 0.02,
                "exposure_limit": 0.15,
                "max_leverage": 10
            },
            "strategies": {},
            "monitoring": {}
        }
        
        # 写入测试配置文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f, default_flow_style=False, allow_unicode=True)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_load_config_basic(self):
        """测试基本配置加载功能"""
        config_manager = ConfigManager(str(self.config_file))
        config = config_manager.load_config()
        
        # 验证配置类型
        assert isinstance(config, AITradingConfig)
        
        # 验证基本配置项
        assert config.system.timezone == "Asia/Shanghai"
        assert config.system.log_level == "INFO"
        assert config.system.trader_id == "TEST_TRADER"
        
        # 验证Redis配置
        assert config.redis.host == "localhost"
        assert config.redis.port == 6379
        
        print("✅ 基本配置加载测试通过")
    
    def test_config_validation_basic(self):
        """测试基本配置验证"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 正常配置应该加载成功
        config = config_manager.load_config()
        assert config is not None
        
        print("✅ 基本配置验证测试通过")
    
    def test_env_override_basic(self):
        """测试基本环境变量覆盖"""
        # 设置环境变量
        os.environ["AI_TRADING_REDIS_HOST"] = "test-redis"
        os.environ["AI_TRADING_LOG_LEVEL"] = "DEBUG"
        
        try:
            config_manager = ConfigManager(str(self.config_file))
            config = config_manager.load_config()
            
            # 验证环境变量覆盖生效
            assert config.redis.host == "test-redis"
            assert config.system.log_level == "DEBUG"
            
            print("✅ 环境变量覆盖测试通过")
            
        finally:
            # 清理环境变量
            for key in ["AI_TRADING_REDIS_HOST", "AI_TRADING_LOG_LEVEL"]:
                if key in os.environ:
                    del os.environ[key]
    
    def test_file_not_found(self):
        """测试文件不存在的处理"""
        config_manager = ConfigManager("nonexistent_config.yaml")
        
        with pytest.raises(FileNotFoundError):
            config_manager.load_config()
        
        print("✅ 文件不存在处理测试通过")


def test_config_data_structure():
    """测试配置数据结构"""
    # 创建临时配置
    temp_dir = tempfile.mkdtemp()
    config_file = Path(temp_dir) / "structure_test.yaml"
    
    config_data = {
        "system": {
            "timezone": "Asia/Shanghai",
            "log_level": "INFO",
            "trader_id": "STRUCTURE_TEST"
        },
        "ai_models": {
            "test_model": {
                "model_path": "test/path",
                "backup_path": "test/backup",
                "version": "1.0.0"
            }
        },
        "redis": {"host": "localhost", "port": 6379, "db": 0},
        "sqlite": {"db_path": "test.db"},
        "risk_management": {"max_trade_risk": 0.02},
        "strategies": {},
        "monitoring": {}
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f)
        
        config_manager = ConfigManager(str(config_file))
        config = config_manager.load_config()
        
        # 验证数据结构
        assert hasattr(config, 'system')
        assert hasattr(config, 'ai_models')
        assert hasattr(config, 'redis')
        assert hasattr(config, 'sqlite')
        assert hasattr(config, 'risk_management')
        
        # 验证嵌套结构
        assert config.system.trader_id == "STRUCTURE_TEST"
        assert "test_model" in config.ai_models
        
        print("✅ 配置数据结构测试通过")
        
    finally:
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    # 运行简化测试
    pytest.main([__file__, "-v", "-s"])