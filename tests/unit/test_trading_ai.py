"""
交易决策AI模块测试

测试TradingAI和TradingAIModel的完整功能，包括：
- 模型加载和初始化
- 实时推理功能
- BTC预测信号集成
- 信号增强算法
- 延迟性能要求
- 错误处理和边界情况

使用真实的配置和接口进行测试，确保生产环境兼容性。
"""

import pytest
import asyncio
import numpy as np
import json
import tempfile
import os
from datetime import datetime, timezone, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import logging

# 设置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入被测试的模块
from src.ai_trading.models.trading_ai import TradingAI, TradingAIModel
from src.ai_trading.models.interfaces import PredictionResult
from src.ai_trading.data.cache_interface import BTCPrediction


class TestTradingAIModel:
    """测试TradingAIModel核心功能"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        return {
            "ai_models": {
                "trading_ai": {
                    "model_path": "models/trading_ai/trading_ai.onnx",
                    "version": "1.0.0",
                    "inference_timeout": 100
                }
            },
            "system": {
                "timezone": "Asia/Shanghai"
            }
        }
    
    @pytest.fixture
    def sample_input_data(self):
        """样本输入数据"""
        # 生成24个时间步的OHLCV数据
        ohlcv_data = []
        base_price = 50000.0
        for i in range(24):
            price_change = np.random.uniform(-0.02, 0.02)  # ±2%的价格变化
            open_price = base_price * (1 + price_change)
            high_price = open_price * (1 + abs(np.random.uniform(0, 0.01)))
            low_price = open_price * (1 - abs(np.random.uniform(0, 0.01)))
            close_price = open_price + np.random.uniform(-100, 100)
            volume = np.random.uniform(1000, 10000)
            
            ohlcv_data.append([open_price, high_price, low_price, close_price, volume])
            base_price = close_price
        
        # 生成24个时间步的技术指标数据（29个指标）
        tech_indicators = []
        for i in range(24):
            indicators = [
                np.random.uniform(0, 100),       # RSI
                np.random.uniform(-1000, 1000),  # MACD
                np.random.uniform(-500, 500),    # MACD Signal
                np.random.uniform(-200, 200),    # MACD Histogram
                np.random.uniform(45000, 55000), # SMA 5
                np.random.uniform(45000, 55000), # SMA 10
                np.random.uniform(45000, 55000), # SMA 20
                np.random.uniform(45000, 55000), # Upper Band
                np.random.uniform(45000, 55000), # Lower Band
                np.random.uniform(0, 1),         # BB Position
                np.random.uniform(0.5, 2.0),     # Volume Ratio
                np.random.uniform(100, 1000),    # ATR
                np.random.uniform(-0.05, 0.05),  # Price Change
                np.random.uniform(-100, 0),      # Williams %R
                np.random.uniform(0, 100),       # Stoch %K
                np.random.uniform(-200, 200),    # CCI
                np.random.uniform(-1000, 1000),  # Momentum
                np.random.uniform(-10, 10),      # ROC
                np.random.uniform(0.5, 2.0),     # ATR Ratio
                np.random.uniform(0, 1),         # Price Position
                np.random.uniform(-0.5, 0.5),    # Volume Change
                np.random.uniform(0, 1000),      # Price Std
                np.random.uniform(0, 1),         # 额外指标1
                np.random.uniform(0, 1),         # 额外指标2
                np.random.uniform(0, 1),         # 额外指标3
                np.random.uniform(0, 1),         # 额外指标4
                np.random.uniform(0, 1),         # 额外指标5
                np.random.uniform(0, 1),         # 额外指标6
                np.random.uniform(0, 1),         # 额外指标7
            ]
            tech_indicators.append(indicators)
        
        return {
            'symbol': 'BTC/USDT',
            'ohlcv_data': ohlcv_data,
            'technical_indicators': tech_indicators,
            'btc_signal': {
                'direction': 'bullish',
                'confidence': 0.8,
                'metadata': {
                    'probabilities': {
                        'bearish': 0.1,
                        'sideways': 0.1,
                        'bullish': 0.8
                    }
                }
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    @pytest.fixture
    def mock_onnx_model(self):
        """模拟ONNX模型"""
        with tempfile.NamedTemporaryFile(suffix='.onnx', delete=False) as f:
            # 创建一个空的ONNX文件
            f.write(b'mock_onnx_model_data')
            temp_path = f.name
        
        yield temp_path
        
        # 清理临时文件
        try:
            os.unlink(temp_path)
        except OSError:
            pass
    
    def test_model_initialization(self, mock_config):
        """测试模型初始化"""
        logger.info("测试交易AI模型初始化...")
        
        model = TradingAIModel(mock_config)
        
        # 验证基本属性
        assert model.sequence_length == 24
        assert model.ohlcv_features == 5
        assert model.tech_features == 29
        assert model.btc_features == 3
        assert model.total_features == 37
        assert model.output_classes == 3
        
        # 验证类别映射
        assert model.class_mapping[0] == "SELL"
        assert model.class_mapping[1] == "HOLD"
        assert model.class_mapping[2] == "BUY"
        
        # 验证方向映射
        assert model.direction_mapping["SELL"] == "bearish"
        assert model.direction_mapping["HOLD"] == "sideways"
        assert model.direction_mapping["BUY"] == "bullish"
        
        logger.info("✓ 交易AI模型初始化测试通过")
    
    def test_input_validation(self, mock_config, sample_input_data):
        """测试输入数据验证"""
        logger.info("测试输入数据验证...")
        
        model = TradingAIModel(mock_config)
        
        # 测试有效输入
        assert model.validate_input_data(sample_input_data) == True
        
        # 测试缺少必需字段
        invalid_data = sample_input_data.copy()
        del invalid_data['symbol']
        assert model.validate_input_data(invalid_data) == False
        
        # 测试OHLCV数据格式错误
        invalid_data = sample_input_data.copy()
        invalid_data['ohlcv_data'] = "invalid_format"
        assert model.validate_input_data(invalid_data) == False
        
        # 测试OHLCV数据形状错误
        invalid_data = sample_input_data.copy()
        invalid_data['ohlcv_data'] = [[1, 2, 3]]  # 形状错误
        assert model.validate_input_data(invalid_data) == False
        
        # 测试技术指标数据格式错误
        invalid_data = sample_input_data.copy()
        invalid_data['technical_indicators'] = "invalid_format"
        assert model.validate_input_data(invalid_data) == False
        
        # 测试技术指标数据形状错误
        invalid_data = sample_input_data.copy()
        invalid_data['technical_indicators'] = [[1, 2, 3]]  # 形状错误
        assert model.validate_input_data(invalid_data) == False
        
        # 测试symbol格式错误
        invalid_data = sample_input_data.copy()
        invalid_data['symbol'] = 123  # 非字符串
        assert model.validate_input_data(invalid_data) == False
        
        # 测试BTC信号格式错误
        invalid_data = sample_input_data.copy()
        invalid_data['btc_signal'] = "invalid_format"  # 非字典
        assert model.validate_input_data(invalid_data) == False
        
        logger.info("✓ 输入数据验证测试通过")
    
    def test_preprocess_input(self, mock_config, sample_input_data):
        """测试输入数据预处理"""
        logger.info("测试输入数据预处理...")
        
        model = TradingAIModel(mock_config)
        
        # 测试有BTC信号的预处理
        processed_input = model._preprocess_input(sample_input_data)
        
        # 验证输出形状
        expected_shape = (1, 24, 37)  # (batch_size, sequence_length, total_features)
        assert processed_input.shape == expected_shape
        
        # 验证数据类型
        assert processed_input.dtype == np.float32
        
        # 测试无BTC信号的预处理
        no_btc_data = sample_input_data.copy()
        del no_btc_data['btc_signal']
        
        processed_input_no_btc = model._preprocess_input(no_btc_data)
        assert processed_input_no_btc.shape == expected_shape
        
        # 验证BTC特征部分使用了中性值
        btc_features = processed_input_no_btc[0, :, -3:]  # 最后3个特征是BTC特征
        expected_neutral = np.array([0.33, 0.34, 0.33])
        np.testing.assert_array_almost_equal(btc_features[0], expected_neutral, decimal=2)
        
        logger.info("✓ 输入数据预处理测试通过")
    
    def test_signal_enhancement_algorithm(self, mock_config):
        """测试信号增强算法"""
        logger.info("测试信号增强算法...")
        
        model = TradingAIModel(mock_config)
        
        # 测试方向一致时的增强
        btc_signal = {
            'direction': 'bullish',
            'confidence': 0.8
        }
        
        enhanced_confidence = model._apply_signal_enhancement("BUY", 0.6, btc_signal)
        expected_enhancement = 0.6 * (1 + 0.3 * 0.8)  # 0.6 * 1.24 = 0.744
        assert abs(enhanced_confidence - expected_enhancement) < 0.001
        
        # 测试方向相反时的减弱
        btc_signal = {
            'direction': 'bearish',
            'confidence': 0.8
        }
        
        enhanced_confidence = model._apply_signal_enhancement("BUY", 0.6, btc_signal)
        expected_reduction = 0.6 * (1 - 0.3 * 0.8 * 0.5)  # 0.6 * 0.88 = 0.528
        assert abs(enhanced_confidence - expected_reduction) < 0.001
        
        # 测试BTC震荡时的调整
        btc_signal = {
            'direction': 'sideways',
            'confidence': 0.7
        }
        
        enhanced_confidence = model._apply_signal_enhancement("BUY", 0.6, btc_signal)
        expected_adjustment = 0.6 * (1 - 0.3 * 0.7 * 0.2)  # 0.6 * 0.958 = 0.5748
        assert abs(enhanced_confidence - expected_adjustment) < 0.001
        
        # 测试无BTC信号时保持原值
        enhanced_confidence = model._apply_signal_enhancement("BUY", 0.6, None)
        assert enhanced_confidence == 0.6
        
        # 测试置信度边界限制
        enhanced_confidence = model._apply_signal_enhancement("BUY", 0.9, {
            'direction': 'bullish',
            'confidence': 1.0
        })
        assert enhanced_confidence <= 1.0  # 不应超过1.0
        
        logger.info("✓ 信号增强算法测试通过")
    
    @patch('src.ai_trading.models.base_model.ort')
    def test_model_loading_mock(self, mock_ort, mock_config, mock_onnx_model):
        """测试模型加载（使用模拟ONNX）"""
        logger.info("测试模型加载...")
        
        # 模拟ONNX运行时
        mock_session = Mock()
        mock_input = Mock()
        mock_input.name = 'input'
        mock_input.shape = [1, 24, 28]
        mock_output = Mock()
        mock_output.name = 'output'
        mock_output.shape = [1, 3]
        
        mock_session.get_inputs.return_value = [mock_input]
        mock_session.get_outputs.return_value = [mock_output]
        mock_ort.InferenceSession.return_value = mock_session
        
        # 更新配置使用临时模型文件
        config = mock_config.copy()
        config['ai_models']['trading_ai']['model_path'] = mock_onnx_model
        
        model = TradingAIModel(config)
        
        # 测试模型加载
        success = model.load_model()
        assert success == True
        assert model.is_model_loaded() == True
        
        # 验证ONNX会话创建
        mock_ort.InferenceSession.assert_called_once()
        
        logger.info("✓ 模型加载测试通过")
    
    @patch('src.ai_trading.models.base_model.ort')
    def test_prediction_mock(self, mock_ort, mock_config, mock_onnx_model, sample_input_data):
        """测试预测功能（使用模拟ONNX）"""
        logger.info("测试预测功能...")
        
        # 模拟ONNX运行时和预测结果
        mock_session = Mock()
        mock_input = Mock()
        mock_input.name = 'input'
        mock_output = Mock()
        mock_output.name = 'output'
        
        mock_session.get_inputs.return_value = [mock_input]
        mock_session.get_outputs.return_value = [mock_output]
        
        # 模拟模型输出：[0.2, 0.3, 0.5] 表示 [SELL, HOLD, BUY] 的概率
        mock_prediction_output = np.array([[0.2, 0.3, 0.5]], dtype=np.float32)
        mock_session.run.return_value = [mock_prediction_output]
        
        mock_ort.InferenceSession.return_value = mock_session
        
        # 更新配置使用临时模型文件
        config = mock_config.copy()
        config['ai_models']['trading_ai']['model_path'] = mock_onnx_model
        
        model = TradingAIModel(config)
        model.load_model()
        
        # 执行预测
        result = model.predict(sample_input_data)
        
        # 验证预测结果
        assert isinstance(result, PredictionResult)
        assert result.direction in ['bearish', 'sideways', 'bullish']
        assert 0.0 <= result.confidence <= 1.0
        assert result.metadata['model_type'] == 'trading_ai'
        assert result.metadata['symbol'] == 'BTC/USDT'
        assert result.metadata['action'] in ['SELL', 'HOLD', 'BUY']
        assert 'inference_time_ms' in result.metadata
        assert 'probabilities' in result.metadata
        
        # 验证信号增强
        assert result.metadata['btc_enhancement_applied'] == True
        assert 'base_confidence' in result.metadata
        assert 'enhanced_confidence' in result.metadata
        
        logger.info("✓ 预测功能测试通过")
    
    def test_performance_requirements(self, mock_config):
        """测试性能要求"""
        logger.info("测试性能要求...")
        
        model = TradingAIModel(mock_config)
        
        # 验证推理超时配置
        assert model.inference_timeout_ms == 100
        
        # 验证BTC权重配置
        assert model.btc_weight == 0.3
        
        logger.info("✓ 性能要求测试通过")


class TestTradingAI:
    """测试TradingAI高级接口"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟完整配置"""
        return {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO"
            },
            "ai_models": {
                "trading_ai": {
                    "model_path": "models/trading_ai/trading_ai.onnx",
                    "version": "1.0.0",
                    "inference_timeout": 100
                }
            },
            "monitoring": {
                "latency_thresholds": {
                    "mainstream_coins": 100,
                    "other_coins": 300
                }
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0
            }
        }
    
    @pytest.fixture
    def sample_coin_data(self):
        """样本币种数据"""
        coin_data = []
        base_price = 50000.0
        
        for i in range(24):
            price_change = np.random.uniform(-0.01, 0.01)
            open_price = base_price * (1 + price_change)
            high_price = open_price * (1 + abs(np.random.uniform(0, 0.005)))
            low_price = open_price * (1 - abs(np.random.uniform(0, 0.005)))
            close_price = open_price + np.random.uniform(-50, 50)
            volume = np.random.uniform(1000, 5000)
            
            coin_data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'timestamp': datetime.now(timezone.utc) - timedelta(minutes=24-i)
            })
            
            base_price = close_price
        
        return coin_data
    
    def test_trading_ai_initialization(self, mock_config):
        """测试TradingAI初始化"""
        logger.info("测试TradingAI初始化...")
        
        trading_ai = TradingAI(mock_config)
        
        # 验证基本属性
        assert trading_ai.config == mock_config
        assert trading_ai.mainstream_threshold == 100
        assert trading_ai.other_threshold == 300
        assert trading_ai.is_initialized == False
        
        # 验证主流币种列表
        assert 'BTC/USDT' in trading_ai.mainstream_coins
        assert 'ETH/USDT' in trading_ai.mainstream_coins
        assert len(trading_ai.mainstream_coins) == 10
        
        # 验证统计信息初始化
        assert trading_ai.prediction_stats['total_predictions'] == 0
        assert trading_ai.prediction_stats['successful_predictions'] == 0
        assert trading_ai.prediction_stats['failed_predictions'] == 0
        
        logger.info("✓ TradingAI初始化测试通过")
    
    @pytest.mark.asyncio
    async def test_initialization_process(self, mock_config):
        """测试初始化过程"""
        logger.info("测试TradingAI初始化过程...")
        
        trading_ai = TradingAI(mock_config)
        
        # 模拟组件初始化
        with patch('src.ai_trading.models.trading_ai.TradingAIModel') as mock_model_class, \
             patch('src.ai_trading.data.nautilus_data_provider.NautilusDataProvider') as mock_data_provider_class, \
             patch('src.ai_trading.data.cache_factory.get_cache') as mock_get_cache:
            
            # 模拟模型类和实例
            mock_model_instance = Mock()
            mock_model_instance.load_model.return_value = True
            mock_model_class.return_value = mock_model_instance
            
            # 模拟数据提供器初始化成功
            mock_data_provider = AsyncMock()
            mock_data_provider.initialize.return_value = True
            mock_data_provider_class.return_value = mock_data_provider
            
            # 模拟缓存接口可用
            mock_cache = Mock()
            mock_cache.is_connected.return_value = True
            mock_get_cache.return_value = mock_cache
            
            # 执行初始化
            success = await trading_ai.initialize()
            
            # 验证初始化结果
            assert success == True
            assert trading_ai.is_initialized == True
            
            # 验证组件初始化调用
            mock_model_class.assert_called_once()
            mock_model_instance.load_model.assert_called_once()
            mock_data_provider.initialize.assert_called_once()
            mock_cache.is_connected.assert_called()
        
        logger.info("✓ TradingAI初始化过程测试通过")
    
    def test_technical_indicators_calculation(self, mock_config, sample_coin_data):
        """测试技术指标计算"""
        logger.info("测试技术指标计算...")
        
        trading_ai = TradingAI(mock_config)
        
        # 计算技术指标
        tech_indicators = trading_ai._calculate_technical_indicators(sample_coin_data)
        
        # 验证输出形状
        assert len(tech_indicators) == 24  # 24个时间步
        assert len(tech_indicators[0]) == 29  # 29个技术指标
        
        # 验证数据类型
        for indicators in tech_indicators:
            for indicator in indicators:
                assert isinstance(indicator, (int, float))
                assert not np.isnan(indicator)  # 确保没有NaN值
        
        # 验证特定指标的合理性
        for i, indicators in enumerate(tech_indicators):
            # RSI应该在0-100之间
            rsi = indicators[3]  # RSI是第4个指标
            assert 0 <= rsi <= 100
            
            # 价格变化率应该在合理范围内
            price_change = indicators[-1]  # 最后一个指标是价格变化率
            assert -1 <= price_change <= 1  # 价格变化率不应超过100%
        
        logger.info("✓ 技术指标计算测试通过")
    
    def test_model_input_preparation(self, mock_config, sample_coin_data):
        """测试模型输入数据准备"""
        logger.info("测试模型输入数据准备...")
        
        trading_ai = TradingAI(mock_config)
        
        # 模拟BTC信号
        btc_signal = {
            'direction': 'bullish',
            'confidence': 0.75,
            'metadata': {
                'probabilities': {
                    'bearish': 0.1,
                    'sideways': 0.15,
                    'bullish': 0.75
                }
            }
        }
        
        # 准备模型输入
        model_input = trading_ai._prepare_model_input(sample_coin_data, 'BTC/USDT', btc_signal)
        
        # 验证输入结构
        assert 'symbol' in model_input
        assert 'ohlcv_data' in model_input
        assert 'technical_indicators' in model_input
        assert 'btc_signal' in model_input
        assert 'timestamp' in model_input
        
        # 验证OHLCV数据
        ohlcv_data = model_input['ohlcv_data']
        assert len(ohlcv_data) == 24
        assert len(ohlcv_data[0]) == 5  # OHLCV
        
        # 验证技术指标数据
        tech_indicators = model_input['technical_indicators']
        assert len(tech_indicators) == 24
        assert len(tech_indicators[0]) == 29
        
        # 验证BTC信号
        assert model_input['btc_signal'] == btc_signal
        assert model_input['symbol'] == 'BTC/USDT'
        
        logger.info("✓ 模型输入数据准备测试通过")
    
    def test_trading_signal_conversion(self, mock_config):
        """测试交易信号转换"""
        logger.info("测试交易信号转换...")
        
        trading_ai = TradingAI(mock_config)
        
        # 创建模拟预测结果
        prediction_result = PredictionResult(
            direction='bullish',
            confidence=0.85,
            timestamp=datetime.now(timezone.utc).isoformat(),
            valid_until=(datetime.now(timezone.utc) + timedelta(minutes=5)).isoformat(),
            metadata={
                'action': 'BUY',
                'base_confidence': 0.75,
                'enhanced_confidence': 0.85,
                'btc_enhancement_applied': True,
                'probabilities': {
                    'sell': 0.1,
                    'hold': 0.05,
                    'buy': 0.85
                },
                'inference_time_ms': 45.2,
                'model_version': '1.0.0',
                'prediction_horizon': '5m'
            }
        )
        
        # 转换为交易信号
        trading_signal = trading_ai._convert_to_trading_signal(prediction_result, 'BTC/USDT')
        
        # 验证交易信号结构
        assert trading_signal['symbol'] == 'BTC/USDT'
        assert trading_signal['action'] == 'BUY'
        assert trading_signal['direction'] == 'bullish'
        assert trading_signal['confidence'] == 0.85
        assert trading_signal['base_confidence'] == 0.75
        assert trading_signal['enhanced_confidence'] == 0.85
        assert trading_signal['btc_enhancement_applied'] == True
        assert trading_signal['inference_time_ms'] == 45.2
        assert 'probabilities' in trading_signal
        assert 'timestamp' in trading_signal
        assert 'valid_until' in trading_signal
        
        logger.info("✓ 交易信号转换测试通过")
    
    def test_prediction_stats_update(self, mock_config):
        """测试预测统计信息更新"""
        logger.info("测试预测统计信息更新...")
        
        trading_ai = TradingAI(mock_config)
        
        # 初始状态验证
        assert trading_ai.prediction_stats['total_predictions'] == 0
        assert trading_ai.prediction_stats['successful_predictions'] == 0
        assert trading_ai.prediction_stats['failed_predictions'] == 0
        
        # 更新成功预测统计
        trading_ai._update_prediction_stats(True, 50.0, True)
        
        assert trading_ai.prediction_stats['total_predictions'] == 1
        assert trading_ai.prediction_stats['successful_predictions'] == 1
        assert trading_ai.prediction_stats['failed_predictions'] == 0
        assert trading_ai.prediction_stats['enhancement_applied_count'] == 1
        assert trading_ai.prediction_stats['avg_inference_time_ms'] == 50.0
        assert trading_ai.prediction_stats['last_error'] is None
        
        # 更新失败预测统计
        trading_ai._update_prediction_stats(False, 120.0, False, "模型推理失败")
        
        assert trading_ai.prediction_stats['total_predictions'] == 2
        assert trading_ai.prediction_stats['successful_predictions'] == 1
        assert trading_ai.prediction_stats['failed_predictions'] == 1
        assert trading_ai.prediction_stats['enhancement_applied_count'] == 1
        assert trading_ai.prediction_stats['avg_inference_time_ms'] == 85.0  # (50+120)/2
        assert trading_ai.prediction_stats['last_error'] == "模型推理失败"
        
        logger.info("✓ 预测统计信息更新测试通过")
    
    def test_status_reporting(self, mock_config):
        """测试状态报告"""
        logger.info("测试状态报告...")
        
        trading_ai = TradingAI(mock_config)
        
        # 获取初始状态
        status = trading_ai.get_status()
        
        # 验证状态结构
        assert 'is_initialized' in status
        assert 'timezone' in status
        assert 'model_loaded' in status
        assert 'data_provider_initialized' in status
        assert 'cache_available' in status
        assert 'mainstream_threshold_ms' in status
        assert 'other_threshold_ms' in status
        assert 'mainstream_coins_count' in status
        assert 'total_predictions' in status
        assert 'successful_predictions' in status
        assert 'failed_predictions' in status
        assert 'success_rate' in status
        assert 'enhancement_rate' in status
        
        # 验证初始值
        assert status['is_initialized'] == False
        assert status['mainstream_threshold_ms'] == 100
        assert status['other_threshold_ms'] == 300
        assert status['mainstream_coins_count'] == 10
        assert status['success_rate'] == 0.0
        assert status['enhancement_rate'] == 0.0
        
        # 模拟一些预测统计
        trading_ai._update_prediction_stats(True, 45.0, True)
        trading_ai._update_prediction_stats(True, 55.0, False)
        trading_ai._update_prediction_stats(False, 150.0, False, "超时")
        
        # 获取更新后的状态
        updated_status = trading_ai.get_status()
        
        # 验证统计计算
        assert updated_status['total_predictions'] == 3
        assert updated_status['successful_predictions'] == 2
        assert updated_status['failed_predictions'] == 1
        assert updated_status['success_rate'] == 2/3
        assert updated_status['enhancement_rate'] == 1/3
        assert updated_status['avg_inference_time_ms'] == (45+55+150)/3
        
        logger.info("✓ 状态报告测试通过")
    
    @pytest.mark.asyncio
    async def test_cleanup_process(self, mock_config):
        """测试清理过程"""
        logger.info("测试清理过程...")
        
        trading_ai = TradingAI(mock_config)
        
        # 模拟已初始化状态
        trading_ai.is_initialized = True
        trading_ai.data_provider = AsyncMock()
        trading_ai.model = Mock()
        
        # 执行清理
        await trading_ai.cleanup()
        
        # 验证清理结果
        assert trading_ai.is_initialized == False
        trading_ai.data_provider.shutdown.assert_called_once()
        trading_ai.model.unload_model.assert_called_once()
        
        logger.info("✓ 清理过程测试通过")


@pytest.mark.asyncio
async def test_integration_trading_ai_complete_flow():
    """完整的交易AI集成测试"""
    logger.info("开始完整的交易AI集成测试...")
    
    # 使用真实配置
    config = {
        "system": {
            "timezone": "Asia/Shanghai",
            "log_level": "INFO"
        },
        "ai_models": {
            "trading_ai": {
                "model_path": "models/trading_ai/trading_ai.onnx",
                "version": "1.0.0",
                "inference_timeout": 100
            }
        },
        "monitoring": {
            "latency_thresholds": {
                "mainstream_coins": 100,
                "other_coins": 300
            }
        },
        "redis": {
            "host": "***************",
            "port": 6379,
            "db": 2,
            "password": "Test@2023"
        }
    }
    
    # 创建TradingAI实例
    trading_ai = TradingAI(config)
    
    try:
        # 测试初始化（使用模拟组件）
        with patch.object(trading_ai, 'model') as mock_model, \
             patch('src.ai_trading.data.nautilus_data_provider.NautilusDataProvider') as mock_data_provider_class, \
             patch('src.ai_trading.data.cache_factory.get_cache') as mock_get_cache:
            
            # 模拟模型
            mock_model_instance = Mock()
            mock_model_instance.load_model.return_value = True
            mock_model_instance.is_model_loaded.return_value = True
            trading_ai.model = mock_model_instance
            
            # 模拟数据提供器
            mock_data_provider = AsyncMock()
            mock_data_provider.initialize.return_value = True
            mock_data_provider.is_initialized = True
            
            # 模拟历史数据
            sample_bars = []
            base_price = 50000.0
            for i in range(24):
                price_change = np.random.uniform(-0.01, 0.01)
                open_price = base_price * (1 + price_change)
                high_price = open_price * (1 + abs(np.random.uniform(0, 0.005)))
                low_price = open_price * (1 - abs(np.random.uniform(0, 0.005)))
                close_price = open_price + np.random.uniform(-50, 50)
                volume = np.random.uniform(1000, 5000)
                
                sample_bars.append({
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume,
                    'timestamp': datetime.now(timezone.utc) - timedelta(minutes=24-i)
                })
                base_price = close_price
            
            mock_data_provider.get_historical_bars.return_value = sample_bars
            mock_data_provider_class.return_value = mock_data_provider
            
            # 模拟缓存和BTC预测
            mock_cache = Mock()
            mock_cache.is_connected.return_value = True
            
            mock_btc_prediction = Mock()
            mock_btc_prediction.to_dict.return_value = {
                'direction': 'bullish',
                'confidence': 0.8,
                'metadata': {
                    'probabilities': {
                        'bearish': 0.1,
                        'sideways': 0.1,
                        'bullish': 0.8
                    }
                }
            }
            mock_cache.get_btc_prediction.return_value = mock_btc_prediction
            mock_get_cache.return_value = mock_cache
            
            # 模拟模型预测结果
            mock_prediction_result = PredictionResult(
                direction='bullish',
                confidence=0.85,
                timestamp=datetime.now(timezone.utc).isoformat(),
                valid_until=(datetime.now(timezone.utc) + timedelta(minutes=5)).isoformat(),
                metadata={
                    'action': 'BUY',
                    'base_confidence': 0.75,
                    'enhanced_confidence': 0.85,
                    'btc_enhancement_applied': True,
                    'probabilities': {
                        'sell': 0.1,
                        'hold': 0.05,
                        'buy': 0.85
                    },
                    'inference_time_ms': 45.2,
                    'model_version': '1.0.0',
                    'prediction_horizon': '5m'
                }
            )
            mock_model_instance.predict.return_value = mock_prediction_result
            
            # 1. 测试初始化
            logger.info("1. 测试初始化...")
            init_success = await trading_ai.initialize()
            assert init_success == True
            assert trading_ai.is_initialized == True
            logger.info("✓ 初始化成功")
            
            # 2. 测试交易信号生成
            logger.info("2. 测试交易信号生成...")
            trading_signal = await trading_ai.predict_trading_signal('BTC/USDT')
            
            # 验证交易信号
            assert trading_signal is not None
            assert trading_signal['symbol'] == 'BTC/USDT'
            assert trading_signal['action'] in ['BUY', 'SELL', 'HOLD']
            assert trading_signal['direction'] in ['bullish', 'bearish', 'sideways']
            assert 0.0 <= trading_signal['confidence'] <= 1.0
            assert 'btc_enhancement_applied' in trading_signal
            assert 'inference_time_ms' in trading_signal
            
            logger.info(f"✓ 交易信号生成成功: {trading_signal['action']} {trading_signal['symbol']} (置信度: {trading_signal['confidence']})")
            
            # 3. 测试性能统计
            logger.info("3. 测试性能统计...")
            status = trading_ai.get_status()
            assert status['total_predictions'] == 1
            assert status['successful_predictions'] == 1
            assert status['success_rate'] == 1.0
            assert status['enhancement_rate'] == 1.0
            logger.info("✓ 性能统计正确")
            
            # 4. 测试多币种处理
            logger.info("4. 测试多币种处理...")
            
            # 测试主流币种
            eth_signal = await trading_ai.predict_trading_signal('ETH/USDT')
            assert eth_signal is not None
            assert eth_signal['symbol'] == 'ETH/USDT'
            logger.info(f"✓ ETH交易信号: {eth_signal['action']} (置信度: {eth_signal['confidence']})")
            
            # 测试非主流币种
            ada_signal = await trading_ai.predict_trading_signal('ADA/USDT')
            assert ada_signal is not None
            assert ada_signal['symbol'] == 'ADA/USDT'
            logger.info(f"✓ ADA交易信号: {ada_signal['action']} (置信度: {ada_signal['confidence']})")
            
            # 5. 测试错误处理
            logger.info("5. 测试错误处理...")
            
            # 模拟数据获取失败
            mock_data_provider.get_historical_bars.return_value = None
            failed_signal = await trading_ai.predict_trading_signal('INVALID/USDT')
            assert failed_signal is None
            
            # 验证错误统计
            status = trading_ai.get_status()
            assert status['failed_predictions'] > 0
            logger.info("✓ 错误处理正确")
            
            # 6. 测试清理
            logger.info("6. 测试清理...")
            await trading_ai.cleanup()
            assert trading_ai.is_initialized == False
            logger.info("✓ 清理完成")
    
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        raise
    
    logger.info("✅ 完整的交易AI集成测试通过")


if __name__ == "__main__":
    # 运行集成测试
    asyncio.run(test_integration_trading_ai_complete_flow())
    print("所有测试完成！")