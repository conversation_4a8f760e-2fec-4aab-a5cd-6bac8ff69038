"""
配置管理系统集成测试

测试配置管理系统在真实环境中的完整功能
"""

import pytest
import tempfile
import os
import time
import threading
from pathlib import Path
import yaml

from src.ai_trading.core.config import Confi<PERSON><PERSON><PERSON><PERSON>, Config<PERSON><PERSON>per, get_config_manager


class TestConfigIntegration:
    """配置管理系统集成测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "integration_config.yaml"
        
        # 创建完整的测试配置
        self.test_config = {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO",
                "trader_id": "INTEGRATION_TEST"
            },
            "ai_models": {
                "btc_predictor": {
                    "model_path": "models/btc_predictor/model.onnx",
                    "backup_path": "models/backup/btc_predictor",
                    "version": "1.0.0",
                    "update_interval": 3600,
                    "inference_timeout": 100,
                    "input_features": ["open", "high", "low", "close", "volume"]
                },
                "trading_ai": {
                    "model_path": "models/trading_ai/model.onnx",
                    "backup_path": "models/backup/trading_ai",
                    "version": "1.0.0",
                    "inference_timeout": 100,
                    "input_features": ["ohlcv", "btc_signal"]
                }
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None,
                "connection_timeout": 5,
                "max_connections": 10
            },
            "sqlite": {
                "db_path": f"{self.temp_dir}/test_ai_trading.db",
                "backup_interval": 3600
            },
            "risk_management": {
                "max_trade_risk": 0.02,
                "exposure_limit": 0.15,
                "max_leverage": 10,
                "volatility_threshold": 0.1,
                "leverage_adjustment_threshold": 0.5,
                "circuit_breaker": {
                    "daily_loss_limit": 0.05,
                    "volatility_limits": {
                        "level_1": 0.03,
                        "level_2": 0.05,
                        "level_3": 0.07
                    }
                }
            },
            "strategies": {
                "trend_following": {
                    "enabled": True,
                    "confidence_threshold": 0.6,
                    "btc_weight": 0.3,
                    "pyramid_sizing": {
                        "initial_position": 0.005,
                        "max_position": 0.02,
                        "increment": 0.005
                    }
                },
                "grid_trading": {
                    "enabled": True,
                    "base_spacing": 0.005,
                    "max_levels": 10,
                    "btc_sideways_threshold": 0.7,
                    "optimization": {
                        "spacing_reduction": 0.8,
                        "level_increase": 1.2
                    }
                }
            },
            "monitoring": {
                "latency_thresholds": {
                    "mainstream_coins": 100,
                    "other_coins": 300,
                    "ai_inference": 50
                },
                "performance_metrics": [
                    "ai_inference_latency",
                    "order_execution_latency",
                    "trade_success_rate",
                    "daily_pnl"
                ]
            },
            "nautilus": {
                "data_clients": {
                    "binance": {
                        "api_key": "test_api_key",
                        "api_secret": "test_api_secret",
                        "testnet": True
                    }
                }
            }
        }
        
        # 写入配置文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f, default_flow_style=False, allow_unicode=True)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_config_lifecycle(self):
        """测试完整的配置生命周期"""
        # 1. 创建配置管理器
        config_manager = ConfigManager(str(self.config_file), enable_hot_reload=True)
        
        # 2. 加载配置
        config = config_manager.load_config()
        assert config.system.trader_id == "INTEGRATION_TEST"
        assert config.system.timezone == "Asia/Shanghai"
        
        # 3. 验证配置结构完整性
        assert "btc_predictor" in config.ai_models
        assert "trading_ai" in config.ai_models
        assert config.redis.host == "localhost"
        assert config.sqlite.db_path.endswith("test_ai_trading.db")
        
        # 4. 测试配置辅助工具
        helper = ConfigHelper(config)
        
        # 验证时区处理
        timezone = helper.get_timezone()
        assert str(timezone) == "Asia/Shanghai"
        
        # 验证Redis URL生成
        redis_url = helper.get_redis_url()
        assert redis_url == "redis://localhost:6379/0"
        
        # 验证策略启用状态
        assert helper.is_strategy_enabled("trend_following") is True
        assert helper.is_strategy_enabled("grid_trading") is True
        
        # 验证延迟阈值
        assert helper.get_latency_threshold("mainstream_coins") == 100
        assert helper.get_latency_threshold("other_coins") == 300
        
        # 5. 测试熔断级别计算
        assert helper.get_circuit_breaker_level(0.02) == 0  # 正常
        assert helper.get_circuit_breaker_level(0.04) == 1  # 降杠杆
        assert helper.get_circuit_breaker_level(0.06) == 2  # 禁用AI
        assert helper.get_circuit_breaker_level(0.08) == 3  # 仅平仓
        
        # 6. 测试杠杆调整判断
        assert helper.should_adjust_leverage(5.0, 6.0) is True   # 差异1.0 > 0.5
        assert helper.should_adjust_leverage(5.0, 5.3) is False  # 差异0.3 < 0.5
        
        # 7. 测试配置备份
        backup_path = config_manager.backup_config()
        assert Path(backup_path).exists()
        
        # 验证备份内容
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_config = yaml.safe_load(f)
        assert backup_config["system"]["trader_id"] == "INTEGRATION_TEST"
        
        print("✅ 完整配置生命周期测试通过")
    
    def test_environment_variable_integration(self):
        """测试环境变量集成"""
        # 设置环境变量
        test_env_vars = {
            'AI_TRADING_REDIS_HOST': 'test-redis-host',
            'AI_TRADING_REDIS_PORT': '6380',
            'AI_TRADING_LOG_LEVEL': 'DEBUG',
            'AI_TRADING_TRADER_ID': 'ENV_TEST_TRADER',
            'AI_TRADING_MAX_LEVERAGE': '5',
            'BINANCE_API_KEY': 'env_api_key',
            'BINANCE_TESTNET': 'false'
        }
        
        # 保存原始环境变量
        original_env = {}
        for key in test_env_vars:
            original_env[key] = os.environ.get(key)
            os.environ[key] = test_env_vars[key]
        
        try:
            # 创建配置管理器并加载配置
            config_manager = ConfigManager(str(self.config_file))
            config = config_manager.load_config()
            
            # 验证环境变量覆盖生效
            assert config.redis.host == 'test-redis-host'
            assert config.redis.port == 6380
            assert config.system.log_level == 'DEBUG'
            assert config.system.trader_id == 'ENV_TEST_TRADER'
            assert config.risk_management.max_leverage == 5
            assert config.nautilus['data_clients']['binance']['api_key'] == 'env_api_key'
            assert config.nautilus['data_clients']['binance']['testnet'] is False
            
            print("✅ 环境变量集成测试通过")
            
        finally:
            # 恢复原始环境变量
            for key, value in original_env.items():
                if value is None:
                    if key in os.environ:
                        del os.environ[key]
                else:
                    os.environ[key] = value
    
    def test_hot_reload_integration(self):
        """测试热重载集成功能"""
        config_manager = ConfigManager(str(self.config_file), enable_hot_reload=True)
        
        # 回调测试变量
        reload_events = []
        
        def reload_callback(new_config):
            reload_events.append({
                'trader_id': new_config.system.trader_id,
                'timestamp': time.time()
            })
        
        # 添加回调并启动热重载
        config_manager.add_reload_callback(reload_callback)
        
        with config_manager:
            # 初始加载
            config = config_manager.load_config()
            assert config.system.trader_id == "INTEGRATION_TEST"
            
            # 修改配置文件
            modified_config = self.test_config.copy()
            modified_config["system"]["trader_id"] = "HOT_RELOAD_TEST"
            modified_config["system"]["log_level"] = "DEBUG"
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(modified_config, f)
            
            # 等待文件系统事件处理
            time.sleep(0.5)
            
            # 验证配置已更新
            updated_config = config_manager.get_config()
            assert updated_config.system.trader_id == "HOT_RELOAD_TEST"
            assert updated_config.system.log_level == "DEBUG"
            
            # 验证回调被触发
            assert len(reload_events) >= 1
            assert reload_events[-1]['trader_id'] == "HOT_RELOAD_TEST"
        
        print("✅ 热重载集成测试通过")
    
    def test_concurrent_access(self):
        """测试并发访问配置"""
        config_manager = ConfigManager(str(self.config_file))
        results = []
        errors = []
        
        def worker_thread(thread_id):
            """工作线程函数"""
            try:
                for i in range(10):
                    config = config_manager.get_config()
                    helper = ConfigHelper(config)
                    
                    # 执行各种配置操作
                    trader_id = config.system.trader_id
                    timezone = helper.get_timezone()
                    redis_url = helper.get_redis_url()
                    
                    results.append({
                        'thread_id': thread_id,
                        'iteration': i,
                        'trader_id': trader_id,
                        'timezone': str(timezone),
                        'redis_url': redis_url
                    })
                    
                    # 短暂休眠
                    time.sleep(0.01)
                    
            except Exception as e:
                errors.append(f"Thread {thread_id}: {e}")
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0, f"并发访问出现错误: {errors}"
        assert len(results) == 50  # 5个线程 × 10次迭代
        
        # 验证所有结果一致
        first_result = results[0]
        for result in results:
            assert result['trader_id'] == first_result['trader_id']
            assert result['timezone'] == first_result['timezone']
            assert result['redis_url'] == first_result['redis_url']
        
        print("✅ 并发访问测试通过")
    
    def test_config_validation_edge_cases(self):
        """测试配置验证边界情况"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 测试边界值配置
        edge_cases = [
            # 最小风险值
            {"risk_management": {"max_trade_risk": 0.001}},
            # 最大风险值
            {"risk_management": {"max_trade_risk": 1.0}},
            # 最小杠杆
            {"risk_management": {"max_leverage": 1}},
            # 最大端口号
            {"redis": {"port": 65535}},
            # 最小端口号
            {"redis": {"port": 1}},
        ]
        
        for i, edge_case in enumerate(edge_cases):
            # 创建边界测试配置
            test_config = self.test_config.copy()
            for key, value in edge_case.items():
                test_config[key].update(value)
            
            edge_file = Path(self.temp_dir) / f"edge_case_{i}.yaml"
            with open(edge_file, 'w', encoding='utf-8') as f:
                yaml.dump(test_config, f)
            
            # 验证边界配置可以正常加载
            edge_manager = ConfigManager(str(edge_file))
            config = edge_manager.load_config()
            
            # 验证配置值正确设置
            for key, expected_values in edge_case.items():
                config_section = getattr(config, key)
                for attr, expected_value in expected_values.items():
                    actual_value = getattr(config_section, attr)
                    assert actual_value == expected_value
        
        print("✅ 配置验证边界情况测试通过")
    
    def test_config_error_recovery(self):
        """测试配置错误恢复"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 首次正常加载
        config = config_manager.load_config()
        assert config.system.trader_id == "INTEGRATION_TEST"
        
        # 创建无效配置文件
        invalid_config = "invalid: yaml: content: ["
        with open(self.config_file, 'w') as f:
            f.write(invalid_config)
        
        # 尝试重新加载应该失败
        with pytest.raises(yaml.YAMLError):
            config_manager.load_config()
        
        # 恢复有效配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f)
        
        # 重新加载应该成功
        recovered_config = config_manager.load_config()
        assert recovered_config.system.trader_id == "INTEGRATION_TEST"
        
        print("✅ 配置错误恢复测试通过")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])