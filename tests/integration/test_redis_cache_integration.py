"""
Redis缓存系统集成测试

该测试模块验证Redis缓存系统的完整功能，包括：
1. Redis连接和基础操作
2. Nautilus Bar对象的序列化和反序列化
3. BTC预测结果的存储和检索
4. 模型状态管理
5. 健康检查和错误处理

注意：需要运行Redis服务器才能执行此测试
"""

import pytest
import time
import json
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
import redis

# Nautilus Trader imports
from nautilus_trader.model.data import Bar, BarType, BarSpecification
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.enums import BarAggregation, PriceType
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.core.datetime import dt_to_unix_nanos

from src.ai_trading.data import RedisCache, BTCPrediction, initialize_cache, get_cache


class TestRedisCacheIntegration:
    """Redis缓存系统集成测试类"""
    
    @pytest.fixture(scope="class")
    def redis_config(self) -> Dict[str, Any]:
        """Redis测试配置"""
        return {
            "host": "***************",
            "port": 6379,
            "db": 2,  # 使用测试数据库
            "password": "Test@2023",
            "connection_timeout": 5,
            "max_connections": 10
        }
    
    @pytest.fixture(scope="class")
    def redis_cache(self, redis_config: Dict[str, Any]) -> RedisCache:
        """创建Redis缓存实例"""
        cache = RedisCache(redis_config)
        assert cache.connect(), "Redis连接失败，请检查Redis服务器是否运行"
        yield cache
        # 清理测试数据
        cache.delete("test_key")
        cache.delete("btc_pred:latest")
        cache.delete("model_status:test_model")
        cache.delete("nautilus_bars:test_bars")
        cache.disconnect()
    
    @pytest.fixture
    def sample_btc_prediction(self) -> BTCPrediction:
        """创建示例BTC预测结果"""
        now = datetime.now(timezone.utc)
        return BTCPrediction(
            direction="bullish",
            confidence=0.85,
            timestamp=now,
            valid_until=now + timedelta(hours=1),
            metadata={
                "model_version": "1.0.0",
                "input_features": ["ohlcv", "volume"],
                "prediction_horizon": 3600
            }
        )
    
    @pytest.fixture
    def sample_nautilus_bars(self) -> List[Bar]:
        """创建示例Nautilus Bar对象列表"""
        bars = []
        instrument_id = InstrumentId.from_str("BTC/USDT.BINANCE")
        
        # 创建正确的BarSpecification
        from nautilus_trader.model.data import BarSpecification
        bar_spec = BarSpecification(
            step=1,
            aggregation=BarAggregation.MINUTE,
            price_type=PriceType.LAST
        )
        
        bar_type = BarType(
            instrument_id=instrument_id,
            bar_spec=bar_spec
        )
        
        base_time = dt_to_unix_nanos(datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc))
        
        for i in range(5):
            bar = Bar(
                bar_type=bar_type,
                open=Price.from_str(f"{50000 + i * 100}.00"),
                high=Price.from_str(f"{50200 + i * 100}.00"),
                low=Price.from_str(f"{49800 + i * 100}.00"),
                close=Price.from_str(f"{50100 + i * 100}.00"),
                volume=Quantity.from_str(f"{100 + i * 10}.0"),
                ts_event=base_time + i * 60_000_000_000,  # 每分钟递增
                ts_init=base_time + i * 60_000_000_000
            )
            bars.append(bar)
        
        return bars
    
    def test_redis_connection_and_basic_operations(self, redis_cache: RedisCache):
        """测试Redis连接和基础操作"""
        print("\n=== 测试Redis连接和基础操作 ===")
        
        # 测试连接状态
        assert redis_cache.is_connected(), "Redis应该处于连接状态"
        print("✓ Redis连接状态正常")
        
        # 测试基础set/get操作
        test_data = {"message": "Hello Redis", "timestamp": datetime.now().isoformat()}
        assert redis_cache.set("test_key", test_data), "设置缓存应该成功"
        print("✓ 缓存设置成功")
        
        retrieved_data = redis_cache.get("test_key")
        assert retrieved_data is not None, "应该能够获取缓存数据"
        assert retrieved_data["message"] == test_data["message"], "获取的数据应该与设置的数据一致"
        print("✓ 缓存获取成功，数据一致")
        
        # 测试exists操作
        assert redis_cache.exists("test_key"), "键应该存在"
        print("✓ 键存在性检查正常")
        
        # 测试delete操作
        assert redis_cache.delete("test_key"), "删除操作应该成功"
        assert not redis_cache.exists("test_key"), "删除后键应该不存在"
        print("✓ 缓存删除成功")
    
    def test_btc_prediction_storage_and_retrieval(self, redis_cache: RedisCache, sample_btc_prediction: BTCPrediction):
        """测试BTC预测结果的存储和检索"""
        print("\n=== 测试BTC预测结果存储和检索 ===")
        
        # 存储BTC预测结果
        assert redis_cache.set_btc_prediction(sample_btc_prediction), "BTC预测结果存储应该成功"
        print(f"✓ BTC预测结果存储成功: {sample_btc_prediction.direction}, 置信度: {sample_btc_prediction.confidence}")
        
        # 检索BTC预测结果
        retrieved_prediction = redis_cache.get_btc_prediction()
        assert retrieved_prediction is not None, "应该能够获取BTC预测结果"
        assert retrieved_prediction.direction == sample_btc_prediction.direction, "方向应该一致"
        assert retrieved_prediction.confidence == sample_btc_prediction.confidence, "置信度应该一致"
        assert retrieved_prediction.metadata == sample_btc_prediction.metadata, "元数据应该一致"
        print("✓ BTC预测结果检索成功，数据完整一致")
        
        # 验证TTL设置
        ttl = redis_cache._client.ttl(redis_cache.BTC_PRED_KEY)
        assert 3800 <= ttl <= 3900, f"TTL应该在3800-3900秒之间，实际: {ttl}"
        print(f"✓ TTL设置正确: {ttl}秒")
    
    def test_nautilus_bars_serialization(self, redis_cache: RedisCache, sample_nautilus_bars: List[Bar]):
        """测试Nautilus Bar对象的序列化和反序列化"""
        print("\n=== 测试Nautilus Bar对象序列化 ===")
        
        # 存储Nautilus Bar对象
        assert redis_cache.set_nautilus_bars("test_bars", sample_nautilus_bars, ttl=300), "Nautilus Bar存储应该成功"
        print(f"✓ 成功存储{len(sample_nautilus_bars)}个Nautilus Bar对象")
        
        # 检索Nautilus Bar对象
        retrieved_bars = redis_cache.get_nautilus_bars("test_bars")
        assert retrieved_bars is not None, "应该能够获取Nautilus Bar对象"
        assert len(retrieved_bars) == len(sample_nautilus_bars), "Bar数量应该一致"
        print(f"✓ 成功检索{len(retrieved_bars)}个Nautilus Bar对象")
        
        # 验证Bar对象数据完整性
        for i, (original, retrieved) in enumerate(zip(sample_nautilus_bars, retrieved_bars)):
            assert original.bar_type == retrieved.bar_type, f"第{i}个Bar的类型应该一致"
            assert original.open == retrieved.open, f"第{i}个Bar的开盘价应该一致"
            assert original.high == retrieved.high, f"第{i}个Bar的最高价应该一致"
            assert original.low == retrieved.low, f"第{i}个Bar的最低价应该一致"
            assert original.close == retrieved.close, f"第{i}个Bar的收盘价应该一致"
            assert original.volume == retrieved.volume, f"第{i}个Bar的成交量应该一致"
            print(f"✓ 第{i+1}个Bar数据验证通过: OHLCV完整一致")
        
        print("✓ 所有Nautilus Bar对象序列化/反序列化验证通过")
    
    def test_model_status_management(self, redis_cache: RedisCache):
        """测试AI模型状态管理"""
        print("\n=== 测试AI模型状态管理 ===")
        
        # 设置模型状态
        model_name = "test_model"
        status = "available"
        metadata = {
            "version": "1.0.0",
            "loaded_at": datetime.now().isoformat(),
            "performance": {"accuracy": 0.85, "latency_ms": 45}
        }
        
        assert redis_cache.set_model_status(model_name, status, metadata), "模型状态设置应该成功"
        print(f"✓ 模型状态设置成功: {model_name} -> {status}")
        
        # 获取模型状态
        retrieved_status = redis_cache.get_model_status(model_name)
        assert retrieved_status is not None, "应该能够获取模型状态"
        assert retrieved_status["status"] == status, "状态应该一致"
        assert retrieved_status["metadata"] == metadata, "元数据应该一致"
        print("✓ 模型状态检索成功，数据完整一致")
        
        # 更新模型状态
        new_status = "unavailable"
        new_metadata = {"error": "模型加载失败", "timestamp": datetime.now().isoformat()}
        assert redis_cache.set_model_status(model_name, new_status, new_metadata), "模型状态更新应该成功"
        
        updated_status = redis_cache.get_model_status(model_name)
        assert updated_status["status"] == new_status, "更新后的状态应该正确"
        print("✓ 模型状态更新成功")
    
    def test_health_check(self, redis_cache: RedisCache):
        """测试健康检查功能"""
        print("\n=== 测试健康检查功能 ===")
        
        health_info = redis_cache.health_check()
        
        # 验证健康检查结果结构
        assert "service" in health_info, "健康检查应该包含服务名称"
        assert "timestamp" in health_info, "健康检查应该包含时间戳"
        assert "status" in health_info, "健康检查应该包含状态"
        assert "connected" in health_info, "健康检查应该包含连接状态"
        assert "latency_ms" in health_info, "健康检查应该包含延迟信息"
        
        print(f"✓ 服务状态: {health_info['status']}")
        print(f"✓ 连接状态: {health_info['connected']}")
        print(f"✓ 延迟: {health_info['latency_ms']}ms")
        print(f"✓ 内存使用: {health_info.get('memory_usage', 'unknown')}")
        
        # 验证连接状态
        assert health_info["connected"], "健康检查应该显示已连接"
        assert health_info["status"] in ["healthy", "warning", "slow"], "状态应该是有效值"
        assert isinstance(health_info["latency_ms"], (int, float)), "延迟应该是数值"
        
        print("✓ 健康检查功能验证通过")
    
    def test_ttl_and_expiration(self, redis_cache: RedisCache):
        """测试TTL和过期功能"""
        print("\n=== 测试TTL和过期功能 ===")
        
        # 设置短TTL的缓存
        test_data = {"message": "这是一个会过期的消息"}
        assert redis_cache.set("ttl_test", test_data, ttl=2), "设置TTL缓存应该成功"
        print("✓ 设置2秒TTL缓存成功")
        
        # 立即检查数据存在
        assert redis_cache.exists("ttl_test"), "数据应该立即存在"
        retrieved_data = redis_cache.get("ttl_test")
        assert retrieved_data is not None, "应该能够立即获取数据"
        print("✓ 数据立即可用")
        
        # 等待过期
        print("等待3秒让缓存过期...")
        time.sleep(3)
        
        # 检查数据已过期
        assert not redis_cache.exists("ttl_test"), "数据应该已过期"
        expired_data = redis_cache.get("ttl_test")
        assert expired_data is None, "过期数据应该返回None"
        print("✓ 缓存过期功能正常")
    
    def test_error_handling(self, redis_config: Dict[str, Any]):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        # 测试无效配置
        invalid_config = redis_config.copy()
        invalid_config["port"] = 9999  # 无效端口
        
        invalid_cache = RedisCache(invalid_config)
        assert not invalid_cache.connect(), "无效配置应该连接失败"
        print("✓ 无效配置连接失败处理正常")
        
        # 测试未连接状态下的操作
        assert not invalid_cache.is_connected(), "应该显示未连接"
        assert not invalid_cache.set("test", "data"), "未连接时设置应该失败"
        assert invalid_cache.get("test") is None, "未连接时获取应该返回None"
        print("✓ 未连接状态错误处理正常")
    
    def test_cache_factory_integration(self, redis_config: Dict[str, Any]):
        """测试缓存工厂集成"""
        print("\n=== 测试缓存工厂集成 ===")
        
        # 准备完整配置
        full_config = {
            "redis": redis_config,
            "system": {"timezone": "Asia/Shanghai"}
        }
        
        # 初始化缓存工厂
        assert initialize_cache(full_config), "缓存工厂初始化应该成功"
        print("✓ 缓存工厂初始化成功")
        
        # 获取缓存实例
        cache = get_cache()
        assert cache is not None, "应该能够获取缓存实例"
        assert cache.is_connected(), "缓存实例应该已连接"
        print("✓ 缓存实例获取成功")
        
        # 测试缓存操作
        test_data = {"factory_test": "success"}
        assert cache.set("factory_test", test_data), "通过工厂获取的缓存应该能正常操作"
        retrieved = cache.get("factory_test")
        assert retrieved == test_data, "数据应该一致"
        print("✓ 缓存工厂操作验证通过")
        
        # 清理
        cache.delete("factory_test")


if __name__ == "__main__":
    """直接运行测试"""
    import sys
    import os
    
    # 添加项目根目录到Python路径
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)
    
    # 配置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])