"""
真实AI模型与配置集成测试

测试使用真实ONNX模型文件和配置文件的AI模型实现。
严格使用真实数据接口，禁止模拟数据生成。
"""

import pytest
import numpy as np
from pathlib import Path

from ai_trading.models import BTCPredictorModel, TradingAIModel, PredictionResult
from ai_trading.core.config import get_config_manager


class TestRealModelsWithConfig:
    """测试真实模型与配置的集成"""
    
    def test_load_models_from_config(self):
        """测试从配置文件加载真实模型"""
        # 检查模型文件是否存在
        btc_model_path = Path("models/btc_predictor/model.onnx")
        trading_model_path = Path("models/trading_ai/trading_ai.onnx")
        
        if not btc_model_path.exists() or not trading_model_path.exists():
            pytest.skip("模型文件不存在，跳过测试")
        
        # 加载配置
        config_manager = get_config_manager("config/ai_trading_config.yaml")
        config = config_manager.load_config()
        
        # 验证配置加载成功
        assert 'btc_predictor' in config.ai_models
        assert 'trading_ai' in config.ai_models
        
        # 从配置创建BTC预测模型
        btc_config = config.ai_models['btc_predictor']
        btc_model_dict = {
            'model_path': btc_config.model_path,
            'version': btc_config.version,
            'model_type': 'onnx'
        }
        btc_model = BTCPredictorModel(btc_model_dict)
        
        # 从配置创建交易决策模型
        trading_config = config.ai_models['trading_ai']
        trading_model_dict = {
            'model_path': trading_config.model_path,
            'version': trading_config.version,
            'model_type': 'onnx'
        }
        trading_model = TradingAIModel(trading_model_dict)
        
        # 验证模型配置
        assert btc_model.model_path == "models/btc_predictor/model.onnx"
        assert trading_model.model_path == "models/trading_ai/trading_ai.onnx"
        
        # 加载模型
        btc_success = btc_model.load_model()
        trading_success = trading_model.load_model()
        
        assert btc_success, "BTC模型加载失败"
        assert trading_success, "交易决策模型加载失败"
        
        # 验证模型信息
        btc_info = btc_model.get_model_info()
        trading_info = trading_model.get_model_info()
        
        assert btc_info.name == "BTCPredictorModel"
        assert btc_info.input_shape == [1, 24, 78]
        assert btc_info.output_shape == [1, 3]
        
        assert trading_info.name == "TradingAIModel"
        assert trading_info.input_shape == [1, 24, 37]
        assert trading_info.output_shape == [1, 3]
    
    def test_model_input_validation_only(self):
        """测试模型输入验证功能（不使用模拟数据）"""
        btc_model = BTCPredictorModel()
        trading_model = TradingAIModel()
        
        # 测试无效输入 - 非字典类型
        assert not btc_model.validate_input_data("invalid")
        assert not btc_model.validate_input_data(None)
        assert not btc_model.validate_input_data(123)
        
        assert not trading_model.validate_input_data("invalid")
        assert not trading_model.validate_input_data(None)
        assert not trading_model.validate_input_data(123)
        
        # 测试无效输入 - 缺少必需字段
        invalid_btc_input = {'ohlcv_data': []}
        assert not btc_model.validate_input_data(invalid_btc_input)
        
        invalid_trading_input = {'market_data': []}
        assert not trading_model.validate_input_data(invalid_trading_input)
        
        # 测试无效输入 - 错误的数据形状
        invalid_btc_shape = {
            'ohlcv_data': [[1, 2, 3]],  # 错误的特征数
            'technical_indicators': [[1] * 73] * 24
        }
        assert not btc_model.validate_input_data(invalid_btc_shape)
        
        invalid_trading_shape = {
            'market_data': [[1, 2]],  # 错误的特征数
            'btc_signal': [[0.3, 0.3, 0.4]] * 24
        }
        assert not trading_model.validate_input_data(invalid_trading_shape)
    
    def test_model_error_handling(self):
        """测试模型错误处理"""
        btc_model = BTCPredictorModel()
        trading_model = TradingAIModel()
        
        # 测试未加载模型时的错误
        with pytest.raises(RuntimeError, match="模型未加载"):
            btc_model.get_model_info()
        
        with pytest.raises(RuntimeError, match="模型未加载"):
            trading_model.get_model_info()
        
        # 测试预测时的错误
        test_data = {'test': 'data'}
        
        with pytest.raises(RuntimeError, match="模型未加载"):
            btc_model.predict(test_data)
        
        with pytest.raises(RuntimeError, match="模型未加载"):
            trading_model.predict(test_data)
    
    def test_model_lifecycle_management(self):
        """测试模型生命周期管理"""
        # 检查模型文件是否存在
        btc_model_path = Path("models/btc_predictor/model.onnx")
        if not btc_model_path.exists():
            pytest.skip("BTC模型文件不存在，跳过测试")
        
        btc_model = BTCPredictorModel({"version": "1.0.0"})
        
        # 初始状态
        assert not btc_model.is_model_loaded()
        
        # 加载模型
        success = btc_model.load_model()
        assert success
        assert btc_model.is_model_loaded()
        
        # 获取模型信息
        info = btc_model.get_model_info()
        assert info.name == "BTCPredictorModel"
        assert info.version == "1.0.0"
        
        # 卸载模型
        unload_success = btc_model.unload_model()
        assert unload_success
        assert not btc_model.is_model_loaded()
        
        # 重新加载
        reload_success = btc_model.reload_model()
        assert reload_success
        assert btc_model.is_model_loaded()
    
    def test_prediction_result_structure(self):
        """测试预测结果数据结构"""
        from datetime import datetime, timezone
        
        # 创建有效的预测结果
        result = PredictionResult(
            direction="bullish",
            confidence=0.8,
            timestamp=datetime.now(timezone.utc).isoformat(),
            metadata={"model_type": "test"}
        )
        
        # 验证基本属性
        assert result.direction == "bullish"
        assert result.confidence == 0.8
        assert result.metadata["model_type"] == "test"
        
        # 测试序列化
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert result_dict["direction"] == "bullish"
        
        json_str = result.to_json()
        assert isinstance(json_str, str)
        
        # 测试反序列化
        restored = PredictionResult.from_json(json_str)
        assert restored.direction == result.direction
        assert restored.confidence == result.confidence
        
        # 测试有效性检查
        assert result.is_valid()
    
    def test_model_configuration_validation(self):
        """测试模型配置验证"""
        # 测试有效配置
        valid_config = {
            "model_path": "models/btc_predictor/model.onnx",
            "version": "1.0.0",
            "model_type": "onnx"
        }
        
        btc_model = BTCPredictorModel(valid_config)
        assert btc_model.model_path == "models/btc_predictor/model.onnx"
        assert btc_model.model_version == "1.0.0"
        assert btc_model.model_type == "onnx"
        
        # 测试默认配置
        default_model = BTCPredictorModel()
        assert default_model.model_path == "models/btc_predictor/model.onnx"
        assert default_model.sequence_length == 24
        assert default_model.feature_count == 78
        assert default_model.output_classes == 3
    
    def test_model_metadata_loading(self):
        """测试模型元数据加载"""
        btc_model = BTCPredictorModel()
        trading_model = TradingAIModel()
        
        # 模型应该尝试加载元数据文件（即使文件不存在也不应该报错）
        assert isinstance(btc_model._model_metadata, dict)
        assert isinstance(trading_model._model_metadata, dict)
        
        # 验证类别映射
        assert btc_model.class_mapping[0] == "bearish"
        assert btc_model.class_mapping[1] == "sideways"
        assert btc_model.class_mapping[2] == "bullish"
        
        assert trading_model.class_mapping[0] == "bearish"
        assert trading_model.class_mapping[1] == "sideways"
        assert trading_model.class_mapping[2] == "bullish"
        
        assert trading_model.action_mapping[0] == "sell"
        assert trading_model.action_mapping[1] == "hold"
        assert trading_model.action_mapping[2] == "buy"


if __name__ == "__main__":
    # 运行真实模型配置测试
    pytest.main([__file__, "-v", "--tb=short"])