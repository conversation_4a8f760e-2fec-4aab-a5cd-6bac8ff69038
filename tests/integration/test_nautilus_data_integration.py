"""
Nautilus DataEngine数据获取集成测试

该测试模块验证通过Nautilus DataEngine获取真实历史数据的完整流程，
确保数据格式与缓存系统的兼容性，以及数据预处理和标准化的正确性。

测试严格遵循以下规范：
1. 强制使用DataEngine.request_bars()获取数据
2. 验证数据格式与Nautilus Bar标准的兼容性
3. 测试数据预处理和技术指标计算
4. 验证缓存系统的数据存储和读取
5. 端到端数据流完整性测试
"""

import pytest
import asyncio
import logging
import yaml
from typing import Dict, Any
from datetime import datetime, timezone
import numpy as np

from src.ai_trading.data.nautilus_data_provider import NautilusDataProvider, get_data_provider
from src.ai_trading.data.cache_factory import initialize_cache, get_cache
from nautilus_trader.model.data import Bar


class TestNautilusDataIntegration:
    """Nautilus DataEngine数据获取集成测试类"""
    
    @pytest.fixture(scope="class")
    def config(self):
        """测试配置"""
        return {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO"
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 1,  # 使用测试数据库
                "password": None,
                "connection_timeout": 5
            },
            "sqlite": {
                "database_path": "data/test_cache.db",
                "connection_timeout": 30,
                "enable_wal": True
            },
            "nautilus": {
                "trader_id": "AI_TRADER_TEST",
                "log_level": "INFO"
            }
        }
    
    @pytest.fixture(scope="class")
    async def data_provider(self, config):
        """数据提供器实例"""
        # 初始化缓存系统
        cache_init_success = initialize_cache(config)
        assert cache_init_success, "缓存系统初始化失败"
        
        # 创建数据提供器
        provider = await get_data_provider(config)
        assert provider is not None, "数据提供器初始化失败"
        
        yield provider
        
        # 清理
        await provider.shutdown()
    
    @pytest.mark.asyncio
    async def test_nautilus_node_initialization(self, data_provider):
        """测试Nautilus TradingNode初始化"""
        # 验证核心组件已正确初始化
        assert data_provider.trading_node is not None, "TradingNode未初始化"
        assert data_provider.data_engine is not None, "DataEngine未初始化"
        assert data_provider.cache is not None, "Cache未初始化"
        
        # 验证TradingNode状态
        assert data_provider.trading_node.is_running, "TradingNode未运行"
        
        print("✅ Nautilus TradingNode初始化测试通过")
    
    @pytest.mark.asyncio
    async def test_historical_data_acquisition(self, data_provider):
        """测试历史数据获取功能"""
        # 测试BTC数据获取
        btc_bars = await data_provider.get_historical_bars(
            symbol="BTC/USDT",
            timeframe="1-MINUTE",
            count=100,
            use_cache=False  # 强制从DataEngine获取
        )
        
        # 验证数据获取成功
        assert btc_bars is not None, "BTC历史数据获取失败"
        assert len(btc_bars) > 0, "BTC历史数据为空"
        assert isinstance(btc_bars[0], Bar), "返回的不是Nautilus Bar对象"
        
        # 验证数据完整性
        for bar in btc_bars[:10]:  # 检查前10条数据
            assert bar.open.as_double() > 0, f"开盘价无效: {bar.open.as_double()}"
            assert bar.high.as_double() > 0, f"最高价无效: {bar.high.as_double()}"
            assert bar.low.as_double() > 0, f"最低价无效: {bar.low.as_double()}"
            assert bar.close.as_double() > 0, f"收盘价无效: {bar.close.as_double()}"
            assert bar.volume.as_double() >= 0, f"成交量无效: {bar.volume.as_double()}"
            
            # 验证OHLC逻辑关系
            assert bar.high.as_double() >= max(bar.open.as_double(), bar.close.as_double()), "最高价逻辑错误"
            assert bar.low.as_double() <= min(bar.open.as_double(), bar.close.as_double()), "最低价逻辑错误"
        
        print(f"✅ 历史数据获取测试通过: 获取{len(btc_bars)}条BTC数据")
        
        # 测试其他主流币种
        test_symbols = ["ETH/USDT", "BNB/USDT"]
        for symbol in test_symbols:
            bars = await data_provider.get_historical_bars(
                symbol=symbol,
                timeframe="1-MINUTE",
                count=50,
                use_cache=False
            )
            assert bars is not None and len(bars) > 0, f"{symbol}数据获取失败"
            print(f"✅ {symbol}数据获取成功: {len(bars)}条")
    
    @pytest.mark.asyncio
    async def test_data_quality_validation(self, data_provider):
        """测试数据质量验证"""
        # 获取测试数据
        btc_bars = await data_provider.get_historical_bars("BTC/USDT", "1-MINUTE", 200)
        assert btc_bars is not None, "测试数据获取失败"
        
        # 执行数据质量验证
        validation_result = data_provider.validate_data_quality(btc_bars, "BTC/USDT")
        
        # 验证结果结构
        assert hasattr(validation_result, 'is_valid'), "验证结果缺少is_valid字段"
        assert hasattr(validation_result, 'bar_count'), "验证结果缺少bar_count字段"
        assert hasattr(validation_result, 'data_quality_score'), "验证结果缺少data_quality_score字段"
        
        # 验证数据质量
        assert validation_result.bar_count == len(btc_bars), "K线数量统计错误"
        assert 0 <= validation_result.data_quality_score <= 1, "数据质量分数范围错误"
        
        # 记录验证结果
        print(f"✅ 数据质量验证完成:")
        print(f"   - 数据有效性: {validation_result.is_valid}")
        print(f"   - K线数量: {validation_result.bar_count}")
        print(f"   - 质量分数: {validation_result.data_quality_score:.3f}")
        print(f"   - 缺失周期: {len(validation_result.missing_periods)}")
        print(f"   - 错误信息: {validation_result.errors}")
        print(f"   - 警告信息: {validation_result.warnings}")
        
        # 如果数据质量不佳，记录详细信息但不失败测试（真实数据可能有缺陷）
        if not validation_result.is_valid:
            print(f"⚠️  数据质量验证未通过，但这可能是真实数据的正常情况")
    
    @pytest.mark.asyncio
    async def test_data_preprocessing(self, data_provider):
        """测试数据预处理功能"""
        # 获取测试数据
        btc_bars = await data_provider.get_historical_bars("BTC/USDT", "1-MINUTE", 100)
        assert btc_bars is not None, "测试数据获取失败"
        
        # 执行数据预处理
        processed_data = data_provider.preprocess_market_data(btc_bars, "BTC/USDT")
        assert processed_data is not None, "数据预处理失败"
        
        # 验证OHLCV数组
        assert processed_data.ohlcv_array.shape[0] == len(btc_bars), "OHLCV数组行数错误"
        assert processed_data.ohlcv_array.shape[1] == 6, "OHLCV数组列数错误"  # timestamp, OHLCV
        
        # 验证技术指标特征
        expected_features = [
            "sma_5", "sma_10", "sma_20", "sma_50",
            "ema_12", "ema_26",
            "macd", "macd_signal", "macd_histogram",
            "rsi",
            "bb_upper", "bb_lower", "bb_middle",
            "atr",
            "volume_sma", "volume_ratio",
            "price_change", "price_change_pct"
        ]
        
        for feature in expected_features:
            assert feature in processed_data.features, f"缺少技术指标: {feature}"
            assert len(processed_data.features[feature]) == len(btc_bars), f"技术指标长度错误: {feature}"
        
        # 验证特征数据有效性
        close_prices = processed_data.ohlcv_array[:, 4]
        assert np.all(close_prices > 0), "收盘价数据无效"
        
        # 验证移动平均线的合理性
        sma_5 = processed_data.features["sma_5"]
        sma_20 = processed_data.features["sma_20"]
        
        # 检查移动平均线的平滑性（短期均线应该更接近当前价格）
        recent_close = close_prices[-10:]  # 最近10个收盘价
        recent_sma5 = sma_5[-10:]
        recent_sma20 = sma_20[-10:]
        
        # SMA5应该比SMA20更接近当前价格
        sma5_deviation = np.mean(np.abs(recent_close - recent_sma5))
        sma20_deviation = np.mean(np.abs(recent_close - recent_sma20))
        assert sma5_deviation <= sma20_deviation, "移动平均线计算逻辑错误"
        
        print(f"✅ 数据预处理测试通过:")
        print(f"   - OHLCV数组形状: {processed_data.ohlcv_array.shape}")
        print(f"   - 技术指标数量: {len(processed_data.features)}")
        print(f"   - 特征列表: {list(processed_data.features.keys())}")
        print(f"   - SMA5平均偏差: {sma5_deviation:.2f}")
        print(f"   - SMA20平均偏差: {sma20_deviation:.2f}")
    
    @pytest.mark.asyncio
    async def test_cache_compatibility(self, data_provider):
        """测试缓存系统兼容性"""
        cache = get_cache()
        assert cache is not None, "缓存系统不可用"
        
        # 获取测试数据
        btc_bars = await data_provider.get_historical_bars("BTC/USDT", "1-MINUTE", 50)
        assert btc_bars is not None, "测试数据获取失败"
        
        # 测试Nautilus Bar对象缓存
        cache_key = "test:nautilus_bars:btc"
        
        # 写入缓存
        cache_success = cache.set_nautilus_bars(cache_key, btc_bars, ttl=300)
        assert cache_success, "Nautilus Bar对象缓存写入失败"
        
        # 从缓存读取
        cached_bars = cache.get_nautilus_bars(cache_key)
        assert cached_bars is not None, "Nautilus Bar对象缓存读取失败"
        assert len(cached_bars) == len(btc_bars), "缓存数据数量不匹配"
        
        # 验证数据完整性
        for i, (original, cached) in enumerate(zip(btc_bars[:5], cached_bars[:5])):
            assert abs(original.open.as_double() - cached.open.as_double()) < 1e-8, f"开盘价不匹配: {i}"
            assert abs(original.high.as_double() - cached.high.as_double()) < 1e-8, f"最高价不匹配: {i}"
            assert abs(original.low.as_double() - cached.low.as_double()) < 1e-8, f"最低价不匹配: {i}"
            assert abs(original.close.as_double() - cached.close.as_double()) < 1e-8, f"收盘价不匹配: {i}"
            assert abs(original.volume.as_double() - cached.volume.as_double()) < 1e-8, f"成交量不匹配: {i}"
        
        print(f"✅ 缓存兼容性测试通过:")
        print(f"   - 缓存写入: 成功")
        print(f"   - 缓存读取: 成功")
        print(f"   - 数据完整性: 验证通过")
        print(f"   - 缓存数据量: {len(cached_bars)}条")
        
        # 清理测试数据
        cache.delete(cache_key)
    
    @pytest.mark.asyncio
    async def test_cache_performance(self, data_provider):
        """测试缓存性能"""
        import time
        
        # 获取测试数据
        btc_bars = await data_provider.get_historical_bars("BTC/USDT", "1-MINUTE", 1000, use_cache=False)
        assert btc_bars is not None, "测试数据获取失败"
        
        cache = get_cache()
        assert cache is not None, "缓存系统不可用"
        
        cache_key = "test:performance:btc_1000"
        
        # 测试缓存写入性能
        start_time = time.time()
        cache_success = cache.set_nautilus_bars(cache_key, btc_bars, ttl=300)
        write_time = time.time() - start_time
        
        assert cache_success, "缓存写入失败"
        
        # 测试缓存读取性能
        start_time = time.time()
        cached_bars = cache.get_nautilus_bars(cache_key)
        read_time = time.time() - start_time
        
        assert cached_bars is not None, "缓存读取失败"
        assert len(cached_bars) == len(btc_bars), "缓存数据数量不匹配"
        
        # 性能要求：1000条数据的缓存操作应在1秒内完成
        assert write_time < 1.0, f"缓存写入性能不达标: {write_time:.3f}s"
        assert read_time < 1.0, f"缓存读取性能不达标: {read_time:.3f}s"
        
        print(f"✅ 缓存性能测试通过:")
        print(f"   - 写入时间: {write_time:.3f}s ({len(btc_bars)}条数据)")
        print(f"   - 读取时间: {read_time:.3f}s ({len(cached_bars)}条数据)")
        print(f"   - 写入速度: {len(btc_bars)/write_time:.0f}条/秒")
        print(f"   - 读取速度: {len(cached_bars)/read_time:.0f}条/秒")
        
        # 清理测试数据
        cache.delete(cache_key)
    
    @pytest.mark.asyncio
    async def test_end_to_end_data_flow(self, data_provider):
        """端到端数据流测试"""
        # 执行完整的端到端测试
        test_result = await data_provider.test_end_to_end_data_flow()
        
        # 验证测试结果结构
        assert "success" in test_result, "测试结果缺少success字段"
        assert "tests" in test_result, "测试结果缺少tests字段"
        assert "timestamp" in test_result, "测试结果缺少timestamp字段"
        
        # 验证各个子测试
        required_tests = [
            "data_acquisition",
            "data_validation", 
            "data_preprocessing",
            "cache_compatibility",
            "performance"
        ]
        
        for test_name in required_tests:
            assert test_name in test_result["tests"], f"缺少子测试: {test_name}"
            test_details = test_result["tests"][test_name]
            assert "success" in test_details, f"子测试{test_name}缺少success字段"
            assert "details" in test_details, f"子测试{test_name}缺少details字段"
        
        # 记录测试结果
        print(f"✅ 端到端数据流测试完成:")
        print(f"   - 整体结果: {'通过' if test_result['success'] else '失败'}")
        
        for test_name, test_details in test_result["tests"].items():
            status = "✅ 通过" if test_details["success"] else "❌ 失败"
            print(f"   - {test_name}: {status}")
            
            if not test_details["success"] and "error" in test_details["details"]:
                print(f"     错误: {test_details['details']['error']}")
        
        # 如果有错误或警告，记录但不失败测试
        if test_result.get("errors"):
            print(f"   - 错误信息: {test_result['errors']}")
        
        if test_result.get("warnings"):
            print(f"   - 警告信息: {test_result['warnings']}")
        
        # 至少数据获取和预处理应该成功
        assert test_result["tests"]["data_acquisition"]["success"], "数据获取测试失败"
        assert test_result["tests"]["data_preprocessing"]["success"], "数据预处理测试失败"
    
    @pytest.mark.asyncio
    async def test_multiple_symbols_concurrent(self, data_provider):
        """测试多币种并发数据获取"""
        test_symbols = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT"]
        
        # 并发获取多个币种的数据
        tasks = []
        for symbol in test_symbols:
            task = data_provider.get_historical_bars(symbol, "1-MINUTE", 100)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证结果
        successful_results = 0
        for i, result in enumerate(results):
            symbol = test_symbols[i]
            if isinstance(result, Exception):
                print(f"⚠️  {symbol}数据获取异常: {result}")
            elif result is None:
                print(f"⚠️  {symbol}数据获取失败")
            else:
                print(f"✅ {symbol}数据获取成功: {len(result)}条")
                successful_results += 1
        
        # 至少应该有一半的币种数据获取成功
        assert successful_results >= len(test_symbols) // 2, f"并发数据获取成功率过低: {successful_results}/{len(test_symbols)}"
        
        print(f"✅ 多币种并发测试完成: {successful_results}/{len(test_symbols)}成功")
    
    @pytest.mark.asyncio
    async def test_data_consistency_across_timeframes(self, data_provider):
        """测试不同时间周期数据的一致性"""
        # 获取不同时间周期的BTC数据
        bars_1m = await data_provider.get_historical_bars("BTC/USDT", "1-MINUTE", 60)
        bars_5m = await data_provider.get_historical_bars("BTC/USDT", "5-MINUTE", 12)
        
        if bars_1m and bars_5m:
            # 验证数据时间范围的合理性
            time_1m_span = bars_1m[-1].ts_init.as_datetime() - bars_1m[0].ts_init.as_datetime()
            time_5m_span = bars_5m[-1].ts_init.as_datetime() - bars_5m[0].ts_init.as_datetime()
            
            print(f"✅ 时间周期一致性测试:")
            print(f"   - 1分钟K线时间跨度: {time_1m_span}")
            print(f"   - 5分钟K线时间跨度: {time_5m_span}")
            print(f"   - 1分钟K线数量: {len(bars_1m)}")
            print(f"   - 5分钟K线数量: {len(bars_5m)}")
        else:
            print("⚠️  无法获取足够的数据进行时间周期一致性测试")


if __name__ == "__main__":
    # 运行测试的示例代码
    import sys
    import os
    
    # 添加项目根目录到Python路径
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])