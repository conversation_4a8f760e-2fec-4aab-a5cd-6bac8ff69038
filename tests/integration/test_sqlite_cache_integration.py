"""
SQLite缓存集成测试

测试SQLite缓存的完整功能，包括数据存储、检索、同步、故障切换等。
使用真实的Nautilus Bar对象和BTC预测数据进行测试。
"""

import pytest
import tempfile
import shutil
import json
import time
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

from nautilus_trader.model.data import Bar, BarType, BarSpecification
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.enums import BarAggregation, PriceType
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.core.datetime import dt_to_unix_nanos

from src.ai_trading.data.sqlite_cache import SQLiteCache
from src.ai_trading.data.redis_cache import RedisCache
from src.ai_trading.data.cache_interface import BTCPrediction
from src.ai_trading.data.cache_factory import CacheFactory, initialize_cache


class TestSQLiteCacheIntegration:
    """SQLite缓存集成测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库路径"""
        temp_dir = tempfile.mkdtemp()
        db_path = Path(temp_dir) / "test_cache.db"
        yield str(db_path)
        # 清理临时文件
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def sqlite_config(self, temp_db_path):
        """SQLite缓存配置"""
        return {
            "db_path": temp_db_path,
            "timeout": 30.0,
            "check_same_thread": False
        }
    
    @pytest.fixture
    def redis_config(self):
        """Redis缓存配置（用于同步测试）"""
        return {
            "host": "***************",
            "port": 6379,
            "db": 2,
            "password": "Test@2023",
            "connection_timeout": 5,
            "max_connections": 10
        }
    
    @pytest.fixture
    def sqlite_cache(self, sqlite_config):
        """创建SQLite缓存实例"""
        cache = SQLiteCache(sqlite_config)
        assert cache.connect(), "SQLite缓存连接失败"
        yield cache
        cache.disconnect()
    
    @pytest.fixture
    def sample_btc_prediction(self):
        """创建示例BTC预测数据"""
        return BTCPrediction(
            direction="bullish",
            confidence=0.75,
            timestamp=datetime.now(timezone.utc),
            valid_until=datetime.now(timezone.utc) + timedelta(hours=1),
            metadata={
                "model_version": "1.0.0",
                "input_features": ["ohlcv", "volume"],
                "prediction_time_ms": 45
            }
        )
    
    @pytest.fixture
    def sample_nautilus_bars(self):
        """创建示例Nautilus Bar对象列表"""
        bars = []
        base_time = dt_to_unix_nanos(datetime.now(timezone.utc))
        
        # 创建BarType
        instrument_id = InstrumentId.from_str("BTC/USDT.BINANCE")
        bar_spec = BarSpecification(
            step=1,
            aggregation=BarAggregation.MINUTE,
            price_type=PriceType.LAST
        )
        bar_type = BarType(
            instrument_id=instrument_id,
            bar_spec=bar_spec
        )
        
        for i in range(5):
            bar = Bar(
                bar_type=bar_type,
                open=Price.from_str(f"{29000 + i * 100}.00"),
                high=Price.from_str(f"{29100 + i * 100}.00"),
                low=Price.from_str(f"{28900 + i * 100}.00"),
                close=Price.from_str(f"{29050 + i * 100}.00"),
                volume=Quantity.from_str(f"{1000 + i * 10}.0"),
                ts_event=base_time + i * 60_000_000_000,  # 每分钟一个Bar
                ts_init=base_time + i * 60_000_000_000
            )
            bars.append(bar)
        
        return bars
    
    def test_sqlite_cache_basic_operations(self, sqlite_cache):
        """测试SQLite缓存基本操作"""
        print("\\n=== 测试SQLite缓存基本操作 ===")
        
        # 测试连接状态
        assert sqlite_cache.is_connected(), "缓存应该已连接"
        print("✓ 连接状态检查通过")
        
        # 测试设置和获取缓存
        test_key = "test_key"
        test_value = {"message": "Hello SQLite", "timestamp": datetime.now().isoformat()}
        
        assert sqlite_cache.set(test_key, test_value), "设置缓存应该成功"
        print("✓ 缓存设置成功")
        
        retrieved_value = sqlite_cache.get(test_key)
        assert retrieved_value is not None, "应该能够获取缓存值"
        assert retrieved_value["message"] == test_value["message"], "缓存值应该匹配"
        print("✓ 缓存获取成功")
        
        # 测试键存在性检查
        assert sqlite_cache.exists(test_key), "键应该存在"
        print("✓ 键存在性检查通过")
        
        # 测试删除缓存
        assert sqlite_cache.delete(test_key), "删除缓存应该成功"
        assert not sqlite_cache.exists(test_key), "键应该不存在"
        print("✓ 缓存删除成功")
    
    def test_sqlite_cache_ttl_functionality(self, sqlite_cache):
        """测试SQLite缓存TTL功能"""
        print("\\n=== 测试SQLite缓存TTL功能 ===")
        
        test_key = "ttl_test_key"
        test_value = {"data": "TTL测试数据"}
        ttl = 2  # 2秒过期
        
        # 设置带TTL的缓存
        assert sqlite_cache.set(test_key, test_value, ttl), "设置带TTL的缓存应该成功"
        print("✓ 带TTL缓存设置成功")
        
        # 立即获取应该成功
        retrieved_value = sqlite_cache.get(test_key)
        assert retrieved_value is not None, "立即获取应该成功"
        assert retrieved_value["data"] == test_value["data"], "数据应该匹配"
        print("✓ TTL缓存立即获取成功")
        
        # 等待过期
        print("等待缓存过期...")
        time.sleep(3)
        
        # 过期后获取应该返回None
        expired_value = sqlite_cache.get(test_key)
        assert expired_value is None, "过期后获取应该返回None"
        print("✓ TTL过期功能正常")
    
    def test_sqlite_btc_prediction_operations(self, sqlite_cache, sample_btc_prediction):
        """测试SQLite BTC预测结果操作"""
        print("\\n=== 测试SQLite BTC预测结果操作 ===")
        
        # 存储BTC预测结果
        assert sqlite_cache.set_btc_prediction(sample_btc_prediction), "存储BTC预测应该成功"
        print(f"✓ BTC预测存储成功: {sample_btc_prediction.direction}, 置信度: {sample_btc_prediction.confidence}")
        
        # 获取BTC预测结果
        retrieved_prediction = sqlite_cache.get_btc_prediction()
        assert retrieved_prediction is not None, "应该能够获取BTC预测结果"
        assert retrieved_prediction.direction == sample_btc_prediction.direction, "方向应该匹配"
        assert retrieved_prediction.confidence == sample_btc_prediction.confidence, "置信度应该匹配"
        print("✓ BTC预测获取成功")
        
        # 验证元数据
        assert retrieved_prediction.metadata == sample_btc_prediction.metadata, "元数据应该匹配"
        print("✓ BTC预测元数据验证通过")
    
    def test_sqlite_model_status_operations(self, sqlite_cache):
        """测试SQLite模型状态操作"""
        print("\\n=== 测试SQLite模型状态操作 ===")
        
        model_name = "btc_predictor"
        status = "available"
        metadata = {
            "version": "1.0.0",
            "loaded_at": datetime.now().isoformat(),
            "performance": {"accuracy": 0.85, "latency_ms": 45}
        }
        
        # 设置模型状态
        assert sqlite_cache.set_model_status(model_name, status, metadata), "设置模型状态应该成功"
        print(f"✓ 模型状态设置成功: {model_name} -> {status}")
        
        # 获取模型状态
        retrieved_status = sqlite_cache.get_model_status(model_name)
        assert retrieved_status is not None, "应该能够获取模型状态"
        assert retrieved_status["status"] == status, "状态应该匹配"
        assert retrieved_status["metadata"] == metadata, "元数据应该匹配"
        print("✓ 模型状态获取成功")
    
    def test_sqlite_nautilus_bars_operations(self, sqlite_cache, sample_nautilus_bars):
        """测试SQLite Nautilus Bar对象操作"""
        print("\\n=== 测试SQLite Nautilus Bar对象操作 ===")
        
        test_key = "BTC_USDT_1m"
        
        # 存储Nautilus Bar对象
        assert sqlite_cache.set_nautilus_bars(test_key, sample_nautilus_bars), "存储Nautilus Bar应该成功"
        print(f"✓ Nautilus Bar存储成功: {len(sample_nautilus_bars)}个Bar对象")
        
        # 获取Nautilus Bar对象
        retrieved_bars = sqlite_cache.get_nautilus_bars(test_key)
        assert retrieved_bars is not None, "应该能够获取Nautilus Bar对象"
        assert len(retrieved_bars) == len(sample_nautilus_bars), "Bar数量应该匹配"
        print("✓ Nautilus Bar获取成功")
        
        # 验证Bar对象内容
        for i, (original, retrieved) in enumerate(zip(sample_nautilus_bars, retrieved_bars)):
            assert original.open == retrieved.open, f"第{i}个Bar的开盘价应该匹配"
            assert original.high == retrieved.high, f"第{i}个Bar的最高价应该匹配"
            assert original.low == retrieved.low, f"第{i}个Bar的最低价应该匹配"
            assert original.close == retrieved.close, f"第{i}个Bar的收盘价应该匹配"
            assert original.volume == retrieved.volume, f"第{i}个Bar的成交量应该匹配"
        
        print("✓ Nautilus Bar内容验证通过")
    
    def test_sqlite_health_check(self, sqlite_cache):
        """测试SQLite健康检查"""
        print("\\n=== 测试SQLite健康检查 ===")
        
        health_info = sqlite_cache.health_check()
        
        assert health_info["service"] == "sqlite_cache", "服务名称应该正确"
        assert health_info["connected"] is True, "连接状态应该为True"
        assert health_info["status"] in ["healthy", "warning", "slow"], "状态应该有效"
        assert "latency_ms" in health_info, "应该包含延迟信息"
        assert "db_size_mb" in health_info, "应该包含数据库大小信息"
        assert "table_stats" in health_info, "应该包含表统计信息"
        
        print(f"✓ 健康检查通过: {health_info['status']}, 延迟: {health_info['latency_ms']}ms")
        print(f"  数据库大小: {health_info['db_size_mb']}MB")
        print(f"  表统计: {health_info['table_stats']}")
    
    def test_sqlite_redis_sync(self, sqlite_cache, redis_config, sample_btc_prediction):
        """测试SQLite与Redis的数据同步"""
        print("\\n=== 测试SQLite与Redis数据同步 ===")
        
        try:
            # 创建Redis缓存实例
            redis_cache = RedisCache(redis_config)
            if not redis_cache.connect():
                print("⚠️  Redis连接失败，跳过同步测试")
                pytest.skip("Redis不可用，跳过同步测试")
            
            print("✓ Redis连接成功")
            
            # 在Redis中设置测试数据
            assert redis_cache.set_btc_prediction(sample_btc_prediction), "Redis中设置BTC预测应该成功"
            assert redis_cache.set_model_status("btc_predictor", "available", {"test": "data"}), "Redis中设置模型状态应该成功"
            print("✓ Redis测试数据设置完成")
            
            # 执行同步
            sync_result = sqlite_cache.sync_from_redis(redis_cache)
            print(f"同步结果: {sync_result}")
            
            # 验证同步结果
            assert sync_result["btc_prediction"] is True, "BTC预测同步应该成功"
            assert "btc_predictor" in sync_result["model_status"], "应该包含模型状态同步结果"
            print("✓ 数据同步成功")
            
            # 验证同步后的数据
            synced_prediction = sqlite_cache.get_btc_prediction()
            assert synced_prediction is not None, "同步后应该能获取BTC预测"
            assert synced_prediction.direction == sample_btc_prediction.direction, "同步后方向应该匹配"
            print("✓ 同步数据验证通过")
            
            # 清理Redis测试数据
            redis_cache.delete("btc_pred:latest")
            redis_cache.delete("model_status:btc_predictor")
            redis_cache.disconnect()
            
        except Exception as e:
            print(f"⚠️  同步测试异常: {e}")
            pytest.skip(f"同步测试失败: {e}")
    
    def test_cache_factory_with_sqlite(self, sqlite_config, redis_config):
        """测试缓存工厂的SQLite集成"""
        print("\\n=== 测试缓存工厂SQLite集成 ===")
        
        # 准备配置
        config = {
            "sqlite": sqlite_config,
            "redis": redis_config
        }
        
        # 初始化缓存工厂
        factory = CacheFactory()
        assert factory.initialize(config), "缓存工厂初始化应该成功"
        print("✓ 缓存工厂初始化成功")
        
        # 获取缓存实例
        cache = factory.get_cache()
        assert cache is not None, "应该能获取缓存实例"
        print("✓ 缓存实例获取成功")
        
        # 测试缓存操作
        test_key = "factory_test"
        test_value = {"factory": "test", "timestamp": datetime.now().isoformat()}
        
        assert cache.set(test_key, test_value), "通过工厂获取的缓存应该能设置数据"
        retrieved_value = cache.get(test_key)
        assert retrieved_value is not None, "应该能获取数据"
        assert retrieved_value["factory"] == "test", "数据应该匹配"
        print("✓ 缓存工厂功能测试通过")
        
        # 健康检查
        health_info = factory.health_check()
        assert "cache_factory" in health_info, "应该包含工厂健康信息"
        print(f"✓ 工厂健康检查: {health_info['cache_factory']['status']}")
        
        # 关闭工厂
        factory.shutdown()
        print("✓ 缓存工厂关闭成功")
    
    def test_sqlite_backup_functionality(self, sqlite_cache, temp_db_path):
        """测试SQLite备份功能"""
        print("\\n=== 测试SQLite备份功能 ===")
        
        # 添加一些测试数据
        test_data = {
            "backup_test": "数据备份测试",
            "timestamp": datetime.now().isoformat()
        }
        assert sqlite_cache.set("backup_test_key", test_data), "设置测试数据应该成功"
        print("✓ 测试数据设置完成")
        
        # 执行备份
        backup_path = str(Path(temp_db_path).parent / "backup_test.db")
        assert sqlite_cache.backup_to_file(backup_path), "数据库备份应该成功"
        print(f"✓ 数据库备份成功: {backup_path}")
        
        # 验证备份文件存在
        assert Path(backup_path).exists(), "备份文件应该存在"
        print("✓ 备份文件验证通过")
        
        # 验证备份文件内容（创建新的连接读取备份）
        backup_cache = SQLiteCache({"db_path": backup_path, "timeout": 30.0, "check_same_thread": False})
        assert backup_cache.connect(), "备份数据库连接应该成功"
        
        backup_data = backup_cache.get("backup_test_key")
        assert backup_data is not None, "备份中应该包含测试数据"
        assert backup_data["backup_test"] == test_data["backup_test"], "备份数据应该匹配"
        print("✓ 备份数据验证通过")
        
        backup_cache.disconnect()


if __name__ == "__main__":
    # 直接运行测试
    import sys
    import os
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])