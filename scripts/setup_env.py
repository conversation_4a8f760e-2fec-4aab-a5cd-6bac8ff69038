#!/usr/bin/env python3
"""
环境设置脚本
用于初始化AI交易系统的开发环境
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 AI交易系统环境设置")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 9):
        print("❌ Python版本过低，需要Python 3.9或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 创建虚拟环境
    venv_name = "venv"
    if not os.path.exists(venv_name):
        if not run_command(f"python -m venv {venv_name}", "创建虚拟环境"):
            sys.exit(1)
    else:
        print(f"✅ 虚拟环境 {venv_name} 已存在")
    
    # 确定激活脚本路径
    if platform.system() == "Windows":
        activate_script = f"{venv_name}\\Scripts\\activate"
        pip_path = f"{venv_name}\\Scripts\\pip"
    else:
        activate_script = f"{venv_name}/bin/activate"
        pip_path = f"{venv_name}/bin/pip"
    
    print(f"📝 要激活虚拟环境，请运行:")
    if platform.system() == "Windows":
        print(f"   {venv_name}\\Scripts\\activate")
    else:
        print(f"   source {venv_name}/bin/activate")
    
    # 升级pip
    if not run_command(f"{pip_path} install --upgrade pip", "升级pip"):
        print("⚠️  pip升级失败，但可以继续")
    
    # 安装依赖
    if not run_command(f"{pip_path} install -r requirements.txt", "安装项目依赖"):
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 安装开发依赖
    if not run_command(f"{pip_path} install -e .[dev]", "安装开发依赖"):
        print("⚠️  开发依赖安装失败，但可以继续")
    
    print("\n🎉 环境设置完成！")
    print("\n📋 下一步:")
    print("1. 激活虚拟环境")
    print("2. 将你的AI模型文件放入 models/ 目录")
    print("3. 编辑 config/ai_trading_config.yaml 配置文件")
    print("4. 运行测试: pytest")
    print("5. 开始开发!")

if __name__ == "__main__":
    main()