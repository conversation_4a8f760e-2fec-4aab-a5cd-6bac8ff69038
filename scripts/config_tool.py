#!/usr/bin/env python3
"""
配置管理工具脚本

提供配置文件的验证、备份、热重载测试等功能
"""

import sys
import argparse
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ai_trading.core.config import ConfigManager, ConfigHelper, get_config_manager
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def validate_config(config_path: str) -> bool:
    """验证配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        bool: 验证是否通过
    """
    try:
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()
        
        print(f"✅ 配置文件验证通过: {config_path}")
        print(f"   - 交易员ID: {config.system.trader_id}")
        print(f"   - 时区: {config.system.timezone}")
        print(f"   - 日志级别: {config.system.log_level}")
        print(f"   - AI模型数量: {len(config.ai_models)}")
        print(f"   - Redis主机: {config.redis.host}:{config.redis.port}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件验证失败: {e}")
        return False


def backup_config(config_path: str, backup_path: str = None) -> bool:
    """备份配置文件
    
    Args:
        config_path: 配置文件路径
        backup_path: 备份文件路径
        
    Returns:
        bool: 备份是否成功
    """
    try:
        config_manager = ConfigManager(config_path)
        backup_file = config_manager.backup_config(backup_path)
        
        print(f"✅ 配置文件已备份到: {backup_file}")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件备份失败: {e}")
        return False


def show_config_info(config_path: str) -> bool:
    """显示配置信息
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        bool: 是否成功
    """
    try:
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()
        helper = ConfigHelper(config)
        
        print("📋 配置信息详情:")
        print(f"   系统配置:")
        print(f"     - 交易员ID: {config.system.trader_id}")
        print(f"     - 时区: {config.system.timezone}")
        print(f"     - 日志级别: {config.system.log_level}")
        
        print(f"   AI模型配置:")
        for name, model_config in config.ai_models.items():
            print(f"     - {name}:")
            print(f"       路径: {model_config.model_path}")
            print(f"       版本: {model_config.version}")
            print(f"       推理超时: {model_config.inference_timeout}ms")
        
        print(f"   数据存储:")
        print(f"     - Redis: {helper.get_redis_url()}")
        print(f"     - SQLite: {helper.get_sqlite_path()}")
        
        print(f"   风险管理:")
        print(f"     - 最大单笔风险: {config.risk_management.max_trade_risk * 100}%")
        print(f"     - 敞口限制: {config.risk_management.exposure_limit * 100}%")
        print(f"     - 最大杠杆: {config.risk_management.max_leverage}x")
        
        print(f"   策略配置:")
        print(f"     - 趋势跟踪: {'启用' if helper.is_strategy_enabled('trend_following') else '禁用'}")
        print(f"     - 网格交易: {'启用' if helper.is_strategy_enabled('grid_trading') else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 显示配置信息失败: {e}")
        return False


def test_hot_reload(config_path: str, duration: int = 30) -> bool:
    """测试热重载功能
    
    Args:
        config_path: 配置文件路径
        duration: 测试持续时间（秒）
        
    Returns:
        bool: 测试是否成功
    """
    try:
        print(f"🔄 开始热重载测试，持续时间: {duration}秒")
        print("   请在测试期间修改配置文件以测试热重载功能")
        
        config_manager = ConfigManager(config_path, enable_hot_reload=True)
        
        # 添加重载回调
        def on_config_reload(new_config):
            print(f"🔄 配置已重新加载: {new_config.system.trader_id}")
        
        config_manager.add_reload_callback(on_config_reload)
        
        # 启动热重载
        with config_manager:
            config = config_manager.load_config()
            print(f"   初始配置加载完成: {config.system.trader_id}")
            
            # 等待指定时间
            import time
            for i in range(duration):
                time.sleep(1)
                if i % 5 == 0:
                    print(f"   测试进行中... {i}/{duration}秒")
        
        print("✅ 热重载测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 热重载测试失败: {e}")
        return False


def export_config_schema(output_path: str) -> bool:
    """导出配置文件模式
    
    Args:
        output_path: 输出文件路径
        
    Returns:
        bool: 导出是否成功
    """
    try:
        # 创建配置模式示例
        schema = {
            "system": {
                "timezone": "Asia/Shanghai",
                "log_level": "INFO",
                "trader_id": "AI_TRADER"
            },
            "ai_models": {
                "btc_predictor": {
                    "model_path": "models/btc_predictor/model.onnx",
                    "version": "1.0.0",
                    "update_interval": 3600,
                    "inference_timeout": 100
                },
                "trading_ai": {
                    "model_path": "models/trading_ai/model.onnx",
                    "version": "1.0.0",
                    "inference_timeout": 100
                }
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None,
                "connection_timeout": 5
            },
            "sqlite": {
                "db_path": "data/ai_trading.db",
                "backup_interval": 3600
            },
            "risk_management": {
                "max_trade_risk": 0.02,
                "exposure_limit": 0.15,
                "max_leverage": 10,
                "volatility_threshold": 0.1,
                "circuit_breaker": {
                    "daily_loss_limit": 0.05
                }
            },
            "strategies": {
                "trend_following": {
                    "enabled": True,
                    "confidence_threshold": 0.6
                },
                "grid_trading": {
                    "enabled": True,
                    "base_spacing": 0.005
                }
            },
            "monitoring": {
                "latency_thresholds": {
                    "mainstream_coins": 100,
                    "other_coins": 300
                }
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(schema, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置模式已导出到: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 导出配置模式失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI交易系统配置管理工具')
    parser.add_argument('--config', '-c', default='config/ai_trading_config.yaml',
                       help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 验证命令
    validate_parser = subparsers.add_parser('validate', help='验证配置文件')
    
    # 备份命令
    backup_parser = subparsers.add_parser('backup', help='备份配置文件')
    backup_parser.add_argument('--output', '-o', help='备份文件路径')
    
    # 信息命令
    info_parser = subparsers.add_parser('info', help='显示配置信息')
    
    # 热重载测试命令
    reload_parser = subparsers.add_parser('test-reload', help='测试热重载功能')
    reload_parser.add_argument('--duration', '-d', type=int, default=30,
                              help='测试持续时间（秒）')
    
    # 导出模式命令
    schema_parser = subparsers.add_parser('export-schema', help='导出配置文件模式')
    schema_parser.add_argument('--output', '-o', default='config_schema.json',
                              help='输出文件路径')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    success = False
    
    if args.command == 'validate':
        success = validate_config(args.config)
    elif args.command == 'backup':
        success = backup_config(args.config, args.output)
    elif args.command == 'info':
        success = show_config_info(args.config)
    elif args.command == 'test-reload':
        success = test_hot_reload(args.config, args.duration)
    elif args.command == 'export-schema':
        success = export_config_schema(args.output)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()