# AI Trading System

基于Nautilus Trader平台的AI驱动加密货币短线交易系统

## 系统架构

- **双AI架构**：BTC预测AI + 合约交易决策AI
- **动态杠杆管理**：基于ATR波动率的智能杠杆调整
- **多层风控体系**：实时风险监控、熔断机制、降级策略
- **渐进式开发**：6个独立可运行的开发阶段

## 项目结构

```
ai-trading-system/
├── src/ai_trading/          # 源代码
│   ├── core/               # 核心模块
│   ├── models/             # AI模型接口
│   ├── strategies/         # 交易策略
│   ├── risk/               # 风险管理
│   └── data/               # 数据处理
├── models/                 # AI模型文件
│   ├── btc_predictor/      # BTC预测模型
│   ├── trading_ai/         # 交易决策模型
│   └── backup/             # 模型备份
├── config/                 # 配置文件
├── tests/                  # 测试代码
├── data/                   # 数据存储
└── docs/                   # 文档
```

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 模型文件准备

请将你的预训练模型文件放置在对应目录：

- BTC预测模型：`models/btc_predictor/btc_predictor.onnx`
- 交易决策模型：`models/trading_ai/trading_ai.onnx`

详细说明请参考 `models/README.md`

### 3. 配置设置

编辑 `config/ai_trading_config.yaml` 文件，设置：
- Redis连接信息
- 模型文件路径
- 风险管理参数
- 交易策略配置

### 4. 运行系统

```bash
# 开发模式
python -m ai_trading.main --config config/ai_trading_config.yaml

# 生产模式
ai-trading --config config/ai_trading_config.yaml --mode production
```

## 开发指南

### 开发阶段

系统采用渐进式开发，共6个阶段：

1. **阶段1**：基础框架搭建 ✅
2. **阶段2**：数据层实现
3. **阶段3**：AI模型集成
4. **阶段4**：Nautilus策略集成
5. **阶段5**：风险管理完善
6. **阶段6**：生产优化

### 测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成覆盖率报告
pytest --cov=ai_trading --cov-report=html
```

### 代码规范

```bash
# 代码格式化
black src/ tests/

# 代码检查
flake8 src/ tests/

# 类型检查
mypy src/
```

## 技术特性

- ✅ **Nautilus集成**：完全基于Nautilus Trader框架
- ✅ **真实数据驱动**：严格使用真实接口，禁止模拟数据
- ✅ **动态风控**：ATR波动率杠杆、分级熔断机制
- ✅ **高性能**：主流币种≤100ms延迟要求
- ✅ **可扩展**：模块化架构，支持策略热插拔

## 许可证

MIT License

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request